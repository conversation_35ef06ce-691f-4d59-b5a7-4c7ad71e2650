/**
 * Script de capture des codes de parrainage
 * Ce script doit être chargé sur toutes les pages de la boutique
 * pour capturer immédiatement les codes de parrainage depuis l'URL
 *
 * LOGIQUE : Le code de parrainage est utilisé uniquement lors de l'inscription
 * au programme de fidélité, pas pendant le checkout.
 */

(function() {
  'use strict';

  // Fonction pour capturer le code de parrainage
  function captureReferralCode() {
    try {
      // Vérifier s'il y a un code de parrainage dans l'URL
      const urlParams = new URLSearchParams(window.location.search);
      const referralCode = urlParams.get('ref');
      
      if (referralCode) {
        // Stocker le code de parrainage dans le localStorage
        localStorage.setItem('loyalty_referral_code', referralCode);
        localStorage.setItem('loyalty_referral_timestamp', Date.now().toString());
        
        console.log(`Code de parrainage capturé: ${referralCode}`);
        
        // Nettoyer l'URL pour éviter que le code reste visible
        const cleanUrl = window.location.pathname + window.location.hash;
        window.history.replaceState({}, document.title, cleanUrl);
        
        // Afficher une notification discrète
        showReferralNotification();
      }
    } catch (error) {
      console.error('Erreur lors de la capture du code de parrainage:', error);
    }
  }

  // Fonction pour afficher une notification discrète
  function showReferralNotification() {
    // Créer une notification temporaire
    const notification = document.createElement('div');
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: #4CAF50;
      color: white;
      padding: 12px 16px;
      border-radius: 6px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      z-index: 1000000;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      font-size: 14px;
      opacity: 0;
      transition: opacity 0.3s ease;
      pointer-events: none;
    `;
    
    notification.textContent = '🎉 Lien de parrainage détecté ! Inscrivez-vous au programme de fidélité pour bénéficier des avantages.';
    
    document.body.appendChild(notification);
    
    // Animation d'apparition
    setTimeout(() => {
      notification.style.opacity = '1';
    }, 100);
    
    // Suppression automatique après 5 secondes
    setTimeout(() => {
      notification.style.opacity = '0';
      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification);
        }
      }, 300);
    }, 5000);
  }

  // Fonction pour vérifier l'expiration du code
  function isReferralCodeValid() {
    const timestamp = localStorage.getItem('loyalty_referral_timestamp');
    if (!timestamp) return false;
    
    const now = Date.now();
    const captureTime = parseInt(timestamp);
    const expirationTime = 24 * 60 * 60 * 1000; // 24 heures
    
    return (now - captureTime) < expirationTime;
  }

  // Fonction pour nettoyer les codes expirés
  function cleanExpiredReferralCode() {
    if (!isReferralCodeValid()) {
      localStorage.removeItem('loyalty_referral_code');
      localStorage.removeItem('loyalty_referral_timestamp');
    }
  }

  // Fonction pour obtenir le code de parrainage valide
  function getReferralCode() {
    cleanExpiredReferralCode();
    return localStorage.getItem('loyalty_referral_code');
  }

  // Exposer la fonction globalement pour le widget
  window.LoyaltyReferral = {
    getReferralCode: getReferralCode,
    isValid: isReferralCodeValid,
    clear: function() {
      localStorage.removeItem('loyalty_referral_code');
      localStorage.removeItem('loyalty_referral_timestamp');
    }
  };

  // Capturer le code immédiatement (le script est defer, donc le DOM est prêt)
  captureReferralCode();

  // Capturer aussi lors des changements d'URL (pour les SPA)
  let lastUrl = location.href;
  new MutationObserver(() => {
    const url = location.href;
    if (url !== lastUrl) {
      lastUrl = url;
      captureReferralCode();
    }
  }).observe(document, { subtree: true, childList: true });

})();
