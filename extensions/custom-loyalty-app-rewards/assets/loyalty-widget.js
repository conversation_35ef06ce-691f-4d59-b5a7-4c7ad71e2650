/**
 * Widget de fidélité - Interface client
 * Version: 1.0.0
 */

/**
 * Formate un nombre pour l'affichage compact (1k, 1.2k, 1M, etc.)
 */
function formatCompactNumber(num) {
  if (num < 1000) return num.toString();

  if (num < 1000000) {
    const k = num / 1000;
    return k % 1 === 0 ? `${k}k` : `${k.toFixed(1)}k`;
  }

  if (num < 1000000000) {
    const m = num / 1000000;
    return m % 1 === 0 ? `${m}M` : `${m.toFixed(1)}M`;
  }

  const b = num / 1000000000;
  return b % 1 === 0 ? `${b}B` : `${b.toFixed(1)}B`;
}

class LoyaltyWidget {
  constructor(options = {}) {
    this.options = {
      shop: options.shop || '',
      customerId: options.customerId || null,
      position: options.position || 'bottom-right',
      primaryColor: options.primaryColor || '#2E7D32',
      secondaryColor: options.secondaryColor || '#4CAF50',
      autoOpen: options.autoOpen || false,
      showOnMobile: options.showOnMobile !== false,
      ...options
    };

    // URL de l'App Proxy Shopify (stable)
    this.apiBaseUrl = `/apps/proxy`;

    this.isOpen = false;
    this.isLoading = false;
    this.customerData = null;
    this.earnWays = [];
    this.redeemWays = [];
    this.nextRewardThreshold = 500; // Par défaut, sera calculé dynamiquement
    this.programInfo = null;
    this.currentView = 'main'; // 'main', 'rewards', 'history'

    this.init();
  }

  init() {
    this.createElements();
    this.bindEvents();
    this.applyCustomStyles();
    this.captureReferralCode();
    this.loadCustomerData();

    // Auto-ouverture si configuré
    if (this.options.autoOpen && !this.hasSeenWidget()) {
      setTimeout(() => this.open(), 2000);
      this.markWidgetSeen();
    }

  }

  createElements() {
    this.container = document.getElementById('loyalty-widget');
    this.trigger = document.getElementById('loyalty-trigger');
    this.panel = document.getElementById('loyalty-panel');
    this.panelBody = this.panel?.querySelector('.loyalty-panel-body');
    this.overlay = document.getElementById('loyalty-overlay');
    this.closeBtn = document.getElementById('loyalty-close');

    // Éléments de contenu
    this.pointsPreview = document.getElementById('points-preview');
    this.loadingState = document.getElementById('loyalty-loading');
    this.guestState = document.getElementById('loyalty-guest');
    this.memberState = document.getElementById('loyalty-member');
    this.errorState = document.getElementById('loyalty-error');

    // Éléments de données client
    this.customerInitials = document.getElementById('customer-initials');
    this.customerName = document.getElementById('customer-name');
    this.customerStatus = document.getElementById('customer-status');
    this.customerPoints = document.getElementById('customer-points');
    this.customerOrders = document.getElementById('customer-orders');
    this.pointsNeeded = document.getElementById('points-needed');
    this.progressFill = document.getElementById('progress-fill');
    this.earnWaysList = document.getElementById('earn-ways');

    // Anciens boutons supprimés - plus utilisés
    this.backFromRewardsBtn = document.getElementById('back-from-rewards');
    this.backFromHistoryBtn = document.getElementById('back-from-history');

    // Sections
    this.mainSection = document.getElementById('loyalty-main-section');
    this.rewardsSection = document.getElementById('loyalty-rewards-section');
    this.historySection = document.getElementById('loyalty-history-section');
    this.couponConfigSection = document.getElementById('loyalty-coupon-config-section');
    this.couponResultSection = document.getElementById('loyalty-coupon-result-section');

    // Nouvelles sections
    this.yourRewardsSection = document.getElementById('loyalty-your-rewards-section');
    this.waysToEarnSection = document.getElementById('loyalty-ways-to-earn-section');
    this.waysToRedeemSection = document.getElementById('loyalty-ways-to-redeem-section');
    this.yourActivitySection = document.getElementById('loyalty-your-activity-section');

    // Listes
    this.rewardsList = document.getElementById('rewards-list');
    this.historyList = document.getElementById('history-list');
    this.yourRewardsList = document.getElementById('your-rewards-list');
    this.pastRewardsList = document.getElementById('past-rewards-list');
    this.waysToEarnList = document.getElementById('ways-to-earn-list');
    this.waysToRedeemList = document.getElementById('ways-to-redeem-list');
    this.activityList = document.getElementById('activity-list');

    // Nouvelle section Ways to Redeem intégrée
    this.redeemOptionsMain = document.getElementById('loyalty-redeem-options');

    // Éléments de coupon
    this.backFromCouponConfigBtn = document.getElementById('back-from-coupon-config');
    this.backFromCouponResultBtn = document.getElementById('back-from-coupon-result');
    this.customerPointsHeader = document.getElementById('customer-points-header');
    this.customerPointsResult = document.getElementById('customer-points-result');
    this.pointsInput = document.getElementById('points-input');
    this.couponValueDisplay = document.getElementById('coupon-value-display');
    this.redeemCouponBtn = document.getElementById('redeem-coupon-btn');
    this.generatedCouponCode = document.getElementById('generated-coupon-code');
    this.copyCouponCodeBtn = document.getElementById('copy-coupon-code');
    this.applyCouponBtn = document.getElementById('apply-coupon-btn');

    // Nouveaux boutons de retour
    this.backFromYourRewardsBtn = document.getElementById('back-from-your-rewards');
    this.backFromWaysToEarnBtn = document.getElementById('back-from-ways-to-earn');
    this.backFromWaysToRedeemBtn = document.getElementById('back-from-ways-to-redeem');
    this.backFromYourActivityBtn = document.getElementById('back-from-your-activity');

    // Onglets de la section Activity
    this.pointsTab = document.getElementById('points-tab');
    this.referralsTab = document.getElementById('referrals-tab');

    // Header du membre (à masquer lors de la navigation)
    this.memberHeader = document.getElementById('loyalty-member-header');

    // Éléments de parrainage
    this.referralSection = document.getElementById('loyalty-referral-section');
    this.referralLink = document.getElementById('referral-link');
    this.referralDescription = document.getElementById('referral-description');
    this.copyReferralBtn = document.getElementById('copy-referral-link');
    this.shareFacebookBtn = document.getElementById('share-facebook');
    this.shareTwitterBtn = document.getElementById('share-twitter');
    this.shareEmailBtn = document.getElementById('share-email');
    this.referralCount = document.getElementById('referral-count');

    // Appliquer la position
    this.container.classList.add(`position-${this.options.position}`);
  }

  bindEvents() {
    // Ouverture/fermeture du widget
    this.trigger.addEventListener('click', () => this.toggle());
    this.closeBtn.addEventListener('click', () => this.close());
    this.overlay.addEventListener('click', () => this.close());

    // Anciens boutons de retour (pour compatibilité)
    this.backFromRewardsBtn?.addEventListener('click', () => this.showMainSection());
    this.backFromHistoryBtn?.addEventListener('click', () => this.showMainSection());
    this.backFromCouponConfigBtn?.addEventListener('click', () => this.showRewardsSection());
    this.backFromCouponResultBtn?.addEventListener('click', () => this.showRewardsSection());

    // Nouveaux event listeners
    this.backFromYourRewardsBtn?.addEventListener('click', () => this.showMainSection());
    this.backFromWaysToEarnBtn?.addEventListener('click', () => this.showMainSection());
    this.backFromWaysToRedeemBtn?.addEventListener('click', () => this.showMainSection());
    this.backFromYourActivityBtn?.addEventListener('click', () => this.showMainSection());

    // Event listeners pour les onglets Activity
    this.pointsTab?.addEventListener('click', () => this.showPointsTab());
    this.referralsTab?.addEventListener('click', () => this.showReferralsTab());

    // Coupon events
    this.pointsInput?.addEventListener('input', () => this.updateCouponValue());
    this.redeemCouponBtn?.addEventListener('click', () => this.createCoupon());
    this.copyCouponCodeBtn?.addEventListener('click', () => this.copyCouponCode());
    this.applyCouponBtn?.addEventListener('click', () => this.applyCouponToCart());

    // Parrainage
    this.copyReferralBtn?.addEventListener('click', () => this.copyReferralLink());
    this.shareFacebookBtn?.addEventListener('click', () => this.shareOnFacebook());
    this.shareTwitterBtn?.addEventListener('click', () => this.shareOnTwitter());
    this.shareEmailBtn?.addEventListener('click', () => this.shareByEmail());

    // Gestion du clavier
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && this.isOpen) {
        this.close();
      }
    });

    // Responsive
    window.addEventListener('resize', () => this.handleResize());
  }

  applyCustomStyles() {
    const root = document.documentElement;
    root.style.setProperty('--loyalty-primary', this.options.primaryColor);
    root.style.setProperty('--loyalty-secondary', this.options.secondaryColor);

    // Masquer sur mobile si configuré
    if (!this.options.showOnMobile && window.innerWidth <= 768) {
      this.container.style.display = 'none';
    }
  }

  captureReferralCode() {
    // Le code de parrainage est maintenant capturé par le script global
    // Nous vérifions simplement s'il y en a un de disponible
    const referralCode = this.getReferralCode();
    if (referralCode) {
      console.log(`Code de parrainage disponible: ${referralCode}`);
    }
  }

  getReferralCode() {
    // Utiliser la fonction globale si disponible, sinon fallback sur localStorage
    if (window.LoyaltyReferral && window.LoyaltyReferral.getReferralCode) {
      return window.LoyaltyReferral.getReferralCode();
    }
    return localStorage.getItem('loyalty_referral_code');
  }



  async loadCustomerData() {
    this.setLoading(true);
    console.log("loadCustomerData ", this.options.customerId);
    try {
      if (!this.options.customerId) {
        this.showGuestState();
        return;
      }

      // Charger les données client via l'App Proxy
      const customerResponse = await fetch(
        `${this.apiBaseUrl}?prepath=customer&shop=${this.options.shop}&logged_in_customer_id=${this.options.customerId}`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      if (!customerResponse.ok) {
        throw new Error(`HTTP ${customerResponse.status}: ${customerResponse.statusText}`);
      }

      const customerData = await customerResponse.json();
      this.customerData = customerData;

      // Charger les façons de gagner, d'échanger, les produits et les infos du programme en parallèle
      await Promise.all([
        this.loadEarnWays(),
        this.loadRedeemWays(),
        this.loadExchangeableProducts(),
        this.loadProgramInfo(),
        this.loadReferralLink()
      ]);

      // Calculer le seuil de la prochaine récompense
      this.calculateNextRewardThreshold();

      // Afficher l'état approprié
      if (customerData.type === 'guest' && customerData.points === 0) {
        console.log("Guest state");
        this.showGuestState();
        console.log("guest state ",     this.guestState )
      } else {
        this.showMemberState();
      }

      this.updatePointsPreview();

    } catch (error) {
      console.error('Erreur lors du chargement des données client:', error);
      this.showErrorState();
    } finally {
      this.setLoading(false);
    }
  }

  async loadEarnWays() {
    try {
      const response = await fetch(
        `${this.apiBaseUrl}?prepath=ways-to-earn&shop=${this.options.shop}`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      if (response.ok) {
        this.earnWays = await response.json();
        this.renderEarnWays();
      }
    } catch (error) {
      console.error('Erreur lors du chargement des façons de gagner:', error);
    }
  }

  async loadRedeemWays() {
    try {
      const response = await fetch(
        `${this.apiBaseUrl}?prepath=ways-to-redeem&shop=${this.options.shop}`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      if (response.ok) {
        this.redeemWays = await response.json();
        // Mettre à jour l'affichage dans le layout principal
        this.renderWaysToRedeemMain();
      }
    } catch (error) {
      console.error('Erreur lors du chargement des façons d\'échanger:', error);
    }
  }

  async loadProgramInfo() {
    try {
      const response = await fetch(
        `${this.apiBaseUrl}?prepath=program-info&shop=${this.options.shop}`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      if (response.ok) {
        this.programInfo = await response.json();

        // Vérifier si le programme est actif et le widget activé
        if (!this.programInfo.isActive || !this.programInfo.widgetEnabled) {
          this.hide();
          return;
        }

        // Appliquer TOUS les paramètres de style personnalisés
        if (this.programInfo.widgetColor) {
          document.documentElement.style.setProperty('--loyalty-primary', this.programInfo.widgetColor);
        }
        if (this.programInfo.widgetSecondaryColor) {
          document.documentElement.style.setProperty('--loyalty-secondary', this.programInfo.widgetSecondaryColor);
        }
        if (this.programInfo.widgetTextColor) {
          document.documentElement.style.setProperty('--loyalty-text-color', this.programInfo.widgetTextColor);
        }

        // Appliquer la couleur de background du widget (toujours blanc par défaut)
        document.documentElement.style.setProperty('--loyalty-widget-background', '#ffffff');

        // Appliquer les paramètres de style avancés
        if (this.programInfo.widgetBorderRadius) {
          const radiusValue = this.programInfo.widgetBorderRadius === 'rounded' ? '16px' :
                             this.programInfo.widgetBorderRadius === 'pill' ? '50px' : '4px';
          document.documentElement.style.setProperty('--loyalty-radius', radiusValue);
        }

        if (this.programInfo.widgetSize) {
          const sizeMultiplier = this.programInfo.widgetSize === 'large' ? '1.2' :
                                this.programInfo.widgetSize === 'small' ? '0.8' : '1';
          document.documentElement.style.setProperty('--loyalty-size-multiplier', sizeMultiplier);
        }

        if (this.programInfo.widgetShadow === false) {
          document.documentElement.style.setProperty('--loyalty-shadow', 'none');
        }

        // Mettre à jour le titre et la description
        this.updateProgramInfo();

        // Masquer la section parrainage si le programme de parrainage n'est pas actif
        if (this.referralSection) {
          this.referralSection.style.display = this.programInfo.referralActive ? 'block' : 'none';
        }

        // Mettre à jour la description de parrainage
        this.updateReferralDescription();
      }
    } catch (error) {
      console.error('Erreur lors du chargement des infos du programme:', error);
    }
  }

  async loadExchangeableProducts() {
    try {
      const response = await fetch(
        `${this.apiBaseUrl}?prepath=client-products&shop=${this.options.shop}`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      if (response.ok) {
        const data = await response.json();
        this.exchangeableProducts = data.products || [];
        this.programInfo = { ...this.programInfo, ...data.programInfo };
      }
    } catch (error) {
      console.error('Erreur lors du chargement des produits échangeables:', error);
      this.exchangeableProducts = [];
    }
  }

  async loadReferralLink() {
    try {
      if (!this.customerData || !this.options.customerId) {
        return;
      }

      const response = await fetch(
        `${this.apiBaseUrl}?prepath=generate-referral&shop=${this.options.shop}&customerId=${this.options.customerId}`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      if (response.ok) {
        const data = await response.json();
        if (data.success && data.referralUrl) {
          this.referralData = {
            url: data.referralUrl,
            code: data.code
          };
          this.updateReferralLink();
        }
      }
    } catch (error) {
      console.error('Erreur lors du chargement du lien de parrainage:', error);
    }
  }

  calculateNextRewardThreshold() {
    if (!this.redeemWays.length) {
      this.nextRewardThreshold = 500; // Valeur par défaut
      return;
    }

    const currentPoints = this.customerData?.points || 0;

    // Trouver la prochaine récompense accessible
    const nextReward = this.redeemWays.find(reward => reward.pointsCost > currentPoints);

    if (nextReward) {
      this.nextRewardThreshold = nextReward.pointsCost;
    } else {
      // Si toutes les récompenses sont accessibles, prendre la plus chère
      this.nextRewardThreshold = Math.max(...this.redeemWays.map(r => r.pointsCost));
    }
  }

  setLoading(loading) {
    this.isLoading = loading;
    this.loadingState.style.display = loading ? 'block' : 'none';
    this.guestState.style.display = this.customerData?.type === 'guest' ? 'block' : this.customerData?.type === 'member' ? 'none' : 'block';
    this.memberState.style.display = this.customerData?.type === 'member' ? 'block' : 'none';
    this.errorState.style.display = 'none';
  }

  showGuestState() {
    this.loadingState.style.display = 'none';
    this.guestState.style.display = 'block';
    this.memberState.style.display = 'none';
    this.errorState.style.display = 'none';
    console.log("showGuestState ", this.guestState.style.display);

    // Mettre à jour l'aperçu des points
    this.updatePointsPreview(0);

    // S'assurer que les informations du programme sont affichées
    if (this.programInfo) {
      this.updateProgramInfo();
    }
  }

  showMemberState() {
    this.loadingState.style.display = 'none';
    this.guestState.style.display = 'none';
    this.memberState.style.display = 'block';
    this.errorState.style.display = 'none';

    this.updateCustomerInfo();
    this.showMainSection(); // Afficher la section principale par défaut

    // S'assurer que les informations du programme sont affichées
    if (this.programInfo) {
      this.updateProgramInfo();
    }

    // Rendre la section Ways to Redeem dans le layout principal
    this.renderWaysToRedeemMain();

    // Mettre à jour le compteur de parrainages
    this.updateReferralCount();
  }

  showErrorState() {
    this.loadingState.style.display = 'none';
    this.guestState.style.display = 'none';
    this.memberState.style.display = 'none';
    this.errorState.style.display = 'block';
  }

  updateCustomerInfo() {
    if (!this.customerData) return;

    const { firstName, lastName, email, type, points, ordersCount } = this.customerData;

    // Nom et initiales
    const fullName = firstName && lastName ? `${firstName} ${lastName}` : email || 'Client';
    const initials = firstName && lastName
      ? `${firstName[0]}${lastName[0]}`.toUpperCase()
      : email ? email[0].toUpperCase() : '?';

    this.customerName.textContent = fullName;
    this.customerInitials.textContent = initials;

    // Statut
    this.customerStatus.textContent = type === 'member' ? 'Membre' : 'Invité';
    this.customerStatus.className = `loyalty-status-badge ${type}`;

    // Points et commandes
    this.customerPoints.textContent = points.toLocaleString();
    this.customerOrders.textContent = ordersCount;

    // Progression vers la prochaine récompense
    this.updateProgress();
  }

  updateProgress() {
    const currentPoints = this.customerData?.points || 0;
    const pointsNeeded = Math.max(0, this.nextRewardThreshold - currentPoints);
    const progressPercent = Math.min(100, (currentPoints / this.nextRewardThreshold) * 100);

    if (pointsNeeded > 0) {
      this.pointsNeeded.textContent = `${pointsNeeded} points`;
    } else {
      this.pointsNeeded.textContent = 'Récompense disponible !';
    }

    this.progressFill.style.width = `${progressPercent}%`;
  }

  updatePointsPreview(points = null) {
    const pointsToShow = points !== null ? points : (this.customerData?.points || 0);
    const pointsCount = this.pointsPreview.querySelector('.points-count');
    if (pointsCount) {
      pointsCount.textContent = formatCompactNumber(pointsToShow);
    }
  }

  hide() {
    this.container.style.display = 'none';
  }

  show() {
    this.container.style.display = '';
  }

  updateProgramInfo() {
    if (!this.programInfo) return;
    // Mettre à jour le titre du programme
    const titleElement = document.getElementById('loyalty-program-title');
    if (titleElement) {
      const programName = this.programInfo.name || 'Programme de fidélité';
      titleElement.textContent = programName;
      console.log('Updated program title to:', programName);
    }

    // Mettre à jour le nom du programme dans l'état guest
    const nameElement = document.getElementById('loyalty-program-name');
    if (nameElement) {
      const programName = this.programInfo.name || 'Programme de fidélité';
      nameElement.textContent = `Rejoignez ${programName} !`;
    }

    // Mettre à jour la description du programme
    const descriptionElement = document.getElementById('loyalty-program-description');
    if (descriptionElement) {
      const description = this.programInfo.description || this.programInfo.welcomeMessage || 'Gagnez des points à chaque achat !';
      descriptionElement.textContent = description;
    }

    // Mettre à jour le nom des points partout
    if (this.programInfo.pointsName) {
      const pointsLabels = document.querySelectorAll('.points-label, .loyalty-stat-label');
      pointsLabels.forEach(label => {
        if (label.textContent && label.textContent.toLowerCase().includes('points')) {
          label.textContent = label.textContent.replace(/points/gi, this.programInfo.pointsName);
        }
      });
    }
  }

  renderEarnWays() {
    if (!this.earnWaysList || !this.earnWays.length) return;

    this.earnWaysList.innerHTML = this.earnWays.map(way => {
      // Formater la description avec la valeur des points
      let pointsDescription = '';
      if (way.earningType === 'increments') {
        pointsDescription = `${way.earningValue} points par €1 dépensé`;
      } else {
        pointsDescription = `${way.earningValue} points fixes`;
      }

      // Choisir l'icône selon le type d'action
      let iconSvg = '';
      switch (way.actionType) {
        case 'order':
          iconSvg = `<path d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.293 2.293c-.63.63-.184 1.707.707 1.707H19M7 13v4a2 2 0 002 2h8a2 2 0 002-2v-4m-8 2h.01M15 15h.01" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>`;
          break;
        case 'signup':
          iconSvg = `<path d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>`;
          break;
        default:
          iconSvg = `<path d="M12 2L15.09 8.26L22 9L17 14L18.18 21L12 17.77L5.82 21L7 14L2 9L8.91 8.26L12 2Z" fill="currentColor"/>`;
      }

      return `
        <div class="loyalty-earn-item">
          <div class="loyalty-earn-icon">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              ${iconSvg}
            </svg>
          </div>
          <div class="loyalty-earn-content">
            <div class="loyalty-earn-title">${way.name}</div>
            <div class="loyalty-earn-description">${pointsDescription}</div>
          </div>
        </div>
      `;
    }).join('');
  }

  // ===== MÉTHODES DE NAVIGATION PRINCIPALES =====

  showMainSection() {
    this.hideAllSections();
    if (this.mainSection) {
      this.mainSection.style.display = 'block';
      this.mainSection.classList.add('loyalty-section-slide-in');
    }
    // Afficher le header du membre sur la page principale
    if (this.memberHeader) {
      this.memberHeader.style.display = 'block';
    }
    this.currentView = 'main';
  }

  showRewardsSection() {
    this.hideAllSections();
    if (this.rewardsSection) {
      this.rewardsSection.style.display = 'block';
      this.rewardsSection.classList.add('loyalty-section-slide-in');
      this.renderWaysToRedeem();
    }
    this.currentView = 'rewards';
  }

  showHistorySection() {
    this.hideAllSections();
    if (this.historySection) {
      this.historySection.style.display = 'block';
      this.historySection.classList.add('loyalty-section-slide-in');
      this.renderHistory();
    }
    this.currentView = 'history';
  }

  hideAllSections() {
    // Masquer toutes les sections principales
    const sections = [
      this.mainSection,
      this.rewardsSection,
      this.historySection,
      this.couponConfigSection,
      this.couponResultSection,
      this.yourRewardsSection,
      this.waysToEarnSection,
      this.waysToRedeemSection,
      this.yourActivitySection
    ];

    sections.forEach(section => {
      if (section) {
        section.style.display = 'none';
        section.classList.remove('loyalty-section-slide-in');
      }
    });

    // Masquer aussi les sections dynamiques créées
    if (this.panelBody) {
      const dynamicSections = this.panelBody.querySelectorAll('.loyalty-configurable-coupon-section, .loyalty-coupon-result-section, .loyalty-reward-detail-section');
      dynamicSections.forEach(section => {
        section.style.display = 'none';
        section.classList.remove('loyalty-section-slide-in');
      });
    }
  }

  // ===== NOUVELLES MÉTHODES DE NAVIGATION =====

  showYourRewardsSection() {
    this.hideAllSections();
    // Masquer le header du membre
    if (this.memberHeader) {
      this.memberHeader.style.display = 'none';
    }
    if (this.yourRewardsSection) {
      this.yourRewardsSection.style.display = 'block';
      this.yourRewardsSection.classList.add('loyalty-section-slide-in');
      this.renderYourRewards();
    }
    this.currentView = 'your-rewards';
  }

  showWaysToEarnSection() {
    this.hideAllSections();
    // Masquer le header du membre
    if (this.memberHeader) {
      this.memberHeader.style.display = 'none';
    }
    if (this.waysToEarnSection) {
      this.waysToEarnSection.style.display = 'block';
      this.waysToEarnSection.classList.add('loyalty-section-slide-in');
      this.renderWaysToEarn();
    }
    this.currentView = 'ways-to-earn';
  }

  showWaysToRedeemSection() {
    this.hideAllSections();
    // Masquer le header du membre
    if (this.memberHeader) {
      this.memberHeader.style.display = 'none';
    }
    if (this.waysToRedeemSection) {
      this.waysToRedeemSection.style.display = 'block';
      this.waysToRedeemSection.classList.add('loyalty-section-slide-in');
      this.renderWaysToRedeem();
    }
    this.currentView = 'ways-to-redeem';
  }

  showYourActivitySection() {
    this.hideAllSections();
    // Masquer le header du membre
    if (this.memberHeader) {
      this.memberHeader.style.display = 'none';
    }
    if (this.yourActivitySection) {
      this.yourActivitySection.style.display = 'block';
      this.yourActivitySection.classList.add('loyalty-section-slide-in');
      this.renderYourActivity();
    }
    this.currentView = 'your-activity';
    this.currentActivityTab = 'points'; // Par défaut sur l'onglet Points
  }

  // ===== MÉTHODES POUR LES ONGLETS ACTIVITY =====

  showPointsTab() {
    this.currentActivityTab = 'points';
    this.updateActivityTabs();
    this.renderYourActivity();
  }

  showReferralsTab() {
    this.currentActivityTab = 'referrals';
    this.updateActivityTabs();
    this.renderReferralsActivity();
  }

  updateActivityTabs() {
    // Mettre à jour l'apparence des onglets
    if (this.pointsTab && this.referralsTab) {
      this.pointsTab.classList.toggle('active', this.currentActivityTab === 'points');
      this.referralsTab.classList.toggle('active', this.currentActivityTab === 'referrals');
    }
  }

  // ===== MÉTHODES DE RENDU =====

  renderWaysToRedeemMain() {
    if (!this.redeemOptionsMain) return;

    if (!this.redeemWays.length) {
      this.redeemOptionsMain.innerHTML = `
        <div style="text-align: center; padding: 40px 20px; color: var(--loyalty-text-secondary);">
          <p>No redemption options available yet.</p>
        </div>
      `;
      return;
    }

    const currentPoints = this.customerData?.points || 0;

    this.redeemOptionsMain.innerHTML = this.redeemWays.map(way => {
      const canAfford = way.isConfigurable ?
        currentPoints >= (way.minPoints || 100) :
        currentPoints >= way.pointsCost;

      const statusClass = canAfford ? 'available' : 'unavailable';

      // Déterminer l'icône selon le type
      let icon = '💰';
      if (way.redeemType === 'discount') icon = '🎫';
      if (way.redeemType === 'product') icon = '🎁';
      if (way.redeemType === 'shipping') icon = '🚚';

      return `
        <div class="loyalty-redeem-option ${statusClass}" ${canAfford ? `onclick="loyaltyWidget.handleRedeemClick('${way.id}')"` : ''}>
          <div class="loyalty-redeem-header">
            <div class="loyalty-redeem-icon">${icon}</div>
            <div class="loyalty-redeem-badge ${canAfford ? '' : 'unavailable'}">
              ${canAfford ? 'Available' : 'Insufficient Points'}
            </div>
          </div>
          <div class="loyalty-redeem-content">
            <h7>${way.name}</h7>
            <div class="loyalty-redeem-description">${way.description}</div>
          </div>
          <div class="loyalty-redeem-cost">
            <div class="loyalty-redeem-points">
              ${way.isConfigurable ?
                `From ${way.minPoints || 100} Points` :
                `${way.pointsCost} Points`
              }
            </div>
            <div class="loyalty-redeem-value">
              ${way.isConfigurable ?
                `€${((way.minPoints || 100) / (this.programInfo?.redemptionRate || 100)).toFixed(2)}+` :
                `€${way.redeemValue}`
              }
            </div>
          </div>
          <div class="loyalty-redeem-action">
            <button class="loyalty-redeem-btn" ${!canAfford ? 'disabled' : ''}>
              ${canAfford ? 'Redeem Now' : `Need ${way.isConfigurable ? way.minPoints || 100 : way.pointsCost} Points`}
            </button>
          </div>
        </div>
      `;
    }).join('');
  }


  async renderYourRewards() {
    // Mettre à jour l'en-tête avec les points actuels
    const pointsHeader = document.getElementById('customer-points-header-rewards');
    if (pointsHeader) {
      pointsHeader.textContent = `${formatCompactNumber(this.customerData?.points || 0)} Points`;
    }

    try {
      // Récupérer les vraies récompenses depuis l'API
      const response = await fetch(
        `${this.apiBaseUrl}?prepath=customer-rewards&shop=${this.options.shop}&customerId=${this.customerData?.customerId || this.options.customerId}`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      if (response.ok) {
        const data = await response.json();
        const availableRewards = data.availableRewards || [];
        const pastRewards = data.pastRewards || [];

        // Mettre à jour le compteur de récompenses
        const rewardsCount = document.getElementById('rewards-count');
        if (rewardsCount) {
          const count = availableRewards.length;
          rewardsCount.textContent = count > 0 ?
            `You have ${count} reward${count > 1 ? 's' : ''} available` :
            'No rewards available';
        }

        // Afficher les récompenses disponibles
        if (this.yourRewardsList) {
          if (availableRewards.length > 0) {
            this.yourRewardsList.innerHTML = availableRewards.map(reward => `
              <div class="loyalty-reward-item available" onclick="loyaltyWidget.useReward('${reward.id}')">
                <div class="loyalty-reward-icon">💰</div>
                <div class="loyalty-reward-content">
                  <div class="loyalty-reward-title">${reward.title}</div>
                  <div class="loyalty-reward-subtitle">${reward.subtitle}</div>
                </div>
                <div class="loyalty-reward-action">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <polyline points="9,18 15,12 9,6"></polyline>
                  </svg>
                </div>
              </div>
            `).join('');
          } else {
            this.yourRewardsList.innerHTML = `
              <div style="text-align: center; padding: 40px 20px; color: var(--loyalty-text-secondary);">
                <p>No rewards available yet. Earn more points to unlock rewards!</p>
              </div>
            `;
          }
        }

        // Afficher les récompenses passées
        if (this.pastRewardsList) {
          if (pastRewards.length > 0) {
            this.pastRewardsList.innerHTML = pastRewards.map(reward => `
              <div class="loyalty-reward-item past">
                <div class="loyalty-reward-icon">💰</div>
                <div class="loyalty-reward-content">
                  <div class="loyalty-reward-title">${reward.title}</div>
                  <div class="loyalty-reward-subtitle">${reward.subtitle}</div>
                </div>
                <div class="loyalty-reward-status">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <polyline points="20,6 9,17 4,12"></polyline>
                  </svg>
                </div>
              </div>
            `).join('');
          } else {
            this.pastRewardsList.innerHTML = `
              <div style="text-align: center; padding: 20px; color: var(--loyalty-text-secondary);">
                <p>No past rewards yet.</p>
              </div>
            `;
          }
        }
      } else {
        throw new Error('Failed to fetch rewards');
      }
    } catch (error) {
      console.error('Erreur lors du chargement des récompenses:', error);

      // Affichage d'erreur
      if (this.yourRewardsList) {
        this.yourRewardsList.innerHTML = `
          <div style="text-align: center; padding: 40px 20px; color: var(--loyalty-text-secondary);">
            <p>Unable to load rewards. Please try again later.</p>
          </div>
        `;
      }
    }
  }

  renderWaysToEarn() {
    // Mettre à jour l'en-tête avec les points actuels
    const pointsHeader = document.getElementById('customer-points-header-earn');
    if (pointsHeader) {
      pointsHeader.textContent = `${formatCompactNumber(this.customerData?.points || 0)} Points`;
    }

    if (!this.earnWays.length) {
      this.waysToEarnList.innerHTML = `
        <div style="text-align: center; padding: 40px 20px; color: var(--loyalty-text-secondary);">
          <p>No ways to earn points configured yet.</p>
        </div>
      `;
      return;
    }

    this.waysToEarnList.innerHTML = this.earnWays.map(way => {
      // Déterminer l'icône selon le type d'action
      let icon = '';
      let description = '';

      switch (way.actionType) {
        case 'order':
          icon = `
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M6 2L3 6v14a2 2 0 002 2h14a2 2 0 002-2V6l-3-4z"></path>
              <line x1="3" y1="6" x2="21" y2="6"></line>
              <path d="M16 10a4 4 0 01-8 0"></path>
            </svg>
          `;
          description = way.earningType === 'increments'
            ? `${way.earningValue} Points for every €1 spent`
            : `${way.earningValue} Points per order`;
          break;
        case 'signup':
          icon = `
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M16 21v-2a4 4 0 00-4-4H5a4 4 0 00-4 4v2"></path>
              <circle cx="8.5" cy="7" r="4"></circle>
              <line x1="20" y1="8" x2="20" y2="14"></line>
              <line x1="23" y1="11" x2="17" y2="11"></line>
            </svg>
          `;
          description = `${way.earningValue} Points for joining the program`;
          break;
        case 'review':
          icon = `
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <polygon points="12,2 15.09,8.26 22,9 17,14 18.18,21 12,17.77 5.82,21 7,14 2,9 8.91,8.26"></polygon>
            </svg>
          `;
          description = `${way.earningValue} Points for writing a review`;
          break;
        case 'referral':
          icon = `
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M17 21v-2a4 4 0 00-4-4H5a4 4 0 00-4 4v2"></path>
              <circle cx="9" cy="7" r="4"></circle>
              <path d="M23 21v-2a4 4 0 00-3-3.87"></path>
              <path d="M16 3.13a4 4 0 010 7.75"></path>
            </svg>
          `;
          description = `${way.earningValue} Points for each successful referral`;
          break;
        default:
          icon = `
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <circle cx="12" cy="12" r="10"></circle>
              <path d="M12 6v6l4 2"></path>
            </svg>
          `;
          description = `${way.earningValue} Points`;
      }

      return `
        <div class="loyalty-earn-item">
          <div class="loyalty-earn-icon">
            ${icon}
          </div>
          <div class="loyalty-earn-content">
            <div class="loyalty-earn-title">${way.name}</div>
            <div class="loyalty-earn-description">${description}</div>
          </div>
        </div>
      `;
    }).join('');
  }

  renderWaysToRedeem() {
    // Mettre à jour l'en-tête avec les points actuels
    const pointsHeader = document.getElementById('customer-points-header-redeem');
    if (pointsHeader) {
      pointsHeader.textContent = `${formatCompactNumber(this.customerData?.points || 0)} Points`;
    }

    if (!this.redeemWays.length) {
      this.waysToRedeemList.innerHTML = `
        <div style="text-align: center; padding: 40px 20px; color: var(--loyalty-text-secondary);">
          <p>No ways to redeem points configured yet.</p>
        </div>
      `;
      return;
    }

    this.waysToRedeemList.innerHTML = this.redeemWays.map(way => {
      // Déterminer l'icône selon le type d'échange
      let icon = '';
      let description = '';
      let buttonText = 'Redeem';
      let isAvailable = this.customerData?.points >= way.pointsCost;

      switch (way.redeemType) {
        case 'discount':
          icon = `
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <circle cx="12" cy="12" r="10"></circle>
              <path d="M16 12l-4-4-4 4"></path>
              <path d="M12 16V8"></path>
            </svg>
          `;
          description = `€${way.redeemValue} discount for ${way.pointsCost} points`;
          break;
        case 'coupon':
          icon = `
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M21 12c0 1.66-1.34 3-3 3H6c-1.66 0-3-1.34-3-3s1.34-3 3-3h12c1.66 0 3 1.34 3 3z"></path>
              <path d="M7 12h10"></path>
            </svg>
          `;
          if (way.isConfigurable) {
            description = `Configurable coupon (${way.minPoints || 100} - ${way.maxPoints || 10000} points)`;
            buttonText = 'Configure';
          } else {
            description = `€${way.redeemValue} coupon for ${way.pointsCost} points`;
          }
          break;
        case 'product':
          icon = `
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M6 2L3 6v14a2 2 0 002 2h14a2 2 0 002-2V6l-3-4z"></path>
              <line x1="3" y1="6" x2="21" y2="6"></line>
              <path d="M16 10a4 4 0 01-8 0"></path>
            </svg>
          `;
          description = `Free product for ${way.pointsCost} points`;
          break;
        case 'shipping':
          icon = `
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M16 3h5v5M21 3l-7 7M11 3H6a2 2 0 00-2 2v14c0 1.1.9 2 2 2h14a2 2 0 002-2v-5"></path>
            </svg>
          `;
          description = `Free shipping for ${way.pointsCost} points`;
          break;
        default:
          icon = `
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <circle cx="12" cy="12" r="10"></circle>
              <path d="M16 12l-4-4-4 4"></path>
              <path d="M12 16V8"></path>
            </svg>
          `;
          description = `${way.pointsCost} points`;
      }

      return `
        <div class="loyalty-redeem-item ${!isAvailable ? 'unavailable' : ''}">
          <div class="loyalty-redeem-icon">
            ${icon}
          </div>
          <div class="loyalty-redeem-content">
            <div class="loyalty-redeem-title">${way.name}</div>
            <div class="loyalty-redeem-description">${description}</div>
          </div>
          <div class="loyalty-redeem-action">
            <button
              class="loyalty-redeem-btn ${!isAvailable ? 'disabled' : ''}"
              ${!isAvailable ? 'disabled' : ''}
              onclick="loyaltyWidget.handleRedeemClick('${way.id}')"
            >
              ${buttonText}
            </button>
          </div>
        </div>
      `;
    }).join('');
  }

  async renderYourActivity() {
    // Mettre à jour l'en-tête avec les points actuels
    const pointsHeader = document.getElementById('customer-points-header-activity');
    if (pointsHeader) {
      pointsHeader.textContent = `${formatCompactNumber(this.customerData?.points || 0)} Points`;
    }

    try {
      // Récupérer l'historique des points depuis l'API
      const response = await fetch(
        `${this.apiBaseUrl}?prepath=customer-history&shop=${this.options.shop}&customerId=${this.customerData?.customerId || this.options.customerId}`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      if (response.ok) {
        const data = await response.json();
        const activities = data.history || [];

        if (this.activityList) {
          if (activities.length > 0) {
            this.activityList.innerHTML = activities.map(activity => {
              const isEarned = activity.points > 0;
              const formattedDate = new Date(activity.createdAt).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric'
              });

              return `
                <div class="loyalty-activity-item">
                  <div class="loyalty-activity-content">
                    <div class="loyalty-activity-description">${activity.description}</div>
                    <div class="loyalty-activity-points ${isEarned ? 'earned' : 'spent'}">
                      ${isEarned ? '+' : ''}${formatCompactNumber(activity.points)} Points
                    </div>
                  </div>
                  <div class="loyalty-activity-date">${formattedDate}</div>
                </div>
              `;
            }).join('');
          } else {
            this.activityList.innerHTML = `
              <div style="text-align: center; padding: 40px 20px; color: var(--loyalty-text-secondary);">
                <p>No activity yet. Start earning points by making purchases!</p>
              </div>
            `;
          }
        }
      } else {
        throw new Error('Failed to fetch activity');
      }
    } catch (error) {
      console.error('Erreur lors du chargement de l\'activité:', error);

      // Affichage d'erreur
      if (this.activityList) {
        this.activityList.innerHTML = `
          <div style="text-align: center; padding: 40px 20px; color: var(--loyalty-text-secondary);">
            <p>Unable to load activity. Please try again later.</p>
          </div>
        `;
      }
    }
  }

  async renderReferralsActivity() {
    // Mettre à jour l'en-tête avec les points actuels
    const pointsHeader = document.getElementById('customer-points-header-activity');
    if (pointsHeader) {
      pointsHeader.textContent = `${formatCompactNumber(this.customerData?.points || 0)} Points`;
    }

    try {
      // Récupérer les données de parrainage depuis l'API
      const response = await fetch(
        `${this.apiBaseUrl}?prepath=customer-referrals&shop=${this.options.shop}&customerId=${this.customerData?.customerId || this.options.customerId}`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      if (response.ok) {
        const data = await response.json();
        const referrals = data.referrals || [];

        if (this.activityList) {
          if (referrals.length > 0) {
            this.activityList.innerHTML = referrals.map(referral => {
              const formattedDate = new Date(referral.createdAt).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric'
              });

              const statusText = referral.status === 'completed' ? 'Completed' : 'Pending';
              const statusClass = referral.status === 'completed' ? 'earned' : 'spent';

              // Gérer l'affichage selon le statut
              let description, pointsDisplay;

              if (referral.status === 'completed') {
                description = `Referred ${referral.referredEmail || 'a friend'}`;
                pointsDisplay = `+${referral.pointsEarned || 0} Points`;
              } else {
                description = `Referral link shared`;
                pointsDisplay = `Pending validation`;
              }

              return `
                <div class="loyalty-activity-item">
                  <div class="loyalty-activity-content">
                    <div class="loyalty-activity-description">
                      ${description}
                    </div>
                    <div class="loyalty-activity-points ${statusClass}">
                      ${pointsDisplay}
                    </div>
                  </div>
                  <div class="loyalty-activity-date">${formattedDate}</div>
                </div>
              `;
            }).join('');
          } else {
            this.activityList.innerHTML = `
              <div style="text-align: center; padding: 40px 20px; color: var(--loyalty-text-secondary);">
                <p>No referrals yet. Start referring friends to earn points!</p>
                <div style="margin-top: 16px;">
                  <button onclick="loyaltyWidget.showMainSection()" style="
                    background: var(--loyalty-primary);
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: var(--loyalty-radius-small);
                    cursor: pointer;
                  ">
                    Share Referral Link
                  </button>
                </div>
              </div>
            `;
          }
        }
      } else {
        throw new Error('Failed to fetch referrals');
      }
    } catch (error) {
      console.error('Erreur lors du chargement des parrainages:', error);

      // Affichage d'erreur ou données simulées
      if (this.activityList) {
        // Afficher des données simulées pour la démonstration
        const simulatedReferrals = [
          {
            id: 1,
            referredEmail: '<EMAIL>',
            status: 'completed',
            pointsEarned: 500,
            createdAt: new Date('2025-06-15')
          },
          {
            id: 2,
            referredEmail: '<EMAIL>',
            status: 'pending',
            pointsEarned: 0,
            createdAt: new Date('2025-06-16')
          }
        ];

        this.activityList.innerHTML = simulatedReferrals.map(referral => {
          const formattedDate = referral.createdAt.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
          });

          const statusText = referral.status === 'completed' ? 'Completed' : 'Pending';
          const statusClass = referral.status === 'completed' ? 'earned' : 'spent';

          return `
            <div class="loyalty-activity-item">
              <div class="loyalty-activity-content">
                <div class="loyalty-activity-description">
                  Referred ${referral.referredEmail}
                </div>
                <div class="loyalty-activity-points ${statusClass}">
                  ${referral.status === 'completed' ? `+${referral.pointsEarned} Points` : statusText}
                </div>
              </div>
              <div class="loyalty-activity-date">${formattedDate}</div>
            </div>
          `;
        }).join('');
      }
    }
  }

  renderHistory() {
    if (!this.historyList) return;

    // Simuler des données d'historique pour l'instant
    const mockHistory = [
      {
        id: 1,
        action: 'Commande #1234',
        points: 50,
        type: 'earned',
        date: new Date(Date.now() - 86400000).toLocaleDateString('fr-FR')
      },
      {
        id: 2,
        action: 'Inscription au programme',
        points: 100,
        type: 'earned',
        date: new Date(Date.now() - 172800000).toLocaleDateString('fr-FR')
      },
      {
        id: 3,
        action: 'Échange produit',
        points: -200,
        type: 'redeemed',
        date: new Date(Date.now() - 259200000).toLocaleDateString('fr-FR')
      }
    ];

    this.historyList.innerHTML = mockHistory.map(item => `
      <div class="loyalty-history-item">
        <div class="loyalty-history-info">
          <div class="loyalty-history-action">${item.action}</div>
          <div class="loyalty-history-date">${item.date}</div>
        </div>
        <div class="loyalty-history-points ${item.type === 'earned' ? 'positive' : 'negative'}">
          ${item.points > 0 ? '+' : ''}${item.points}
        </div>
      </div>
    `).join('');

    if (mockHistory.length === 0) {
      this.historyList.innerHTML = `
        <div style="text-align: center; padding: 40px 20px; color: var(--loyalty-text-secondary);">
          <p>Aucun historique disponible.</p>
        </div>
      `;
    }
  }

  updateReferralLink() {
    if (this.referralData && this.referralLink) {
      this.referralLink.value = this.referralData.url;
    }
  }

  async updateReferralDescription() {
    try {
      // Récupérer les paramètres de parrainage depuis l'API
      const response = await fetch(
        `${this.apiBaseUrl}?prepath=program-info&shop=${this.options.shop}`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      if (response.ok && this.referralDescription) {
        const data = await response.json();

        // Construire le message basé sur les vraies récompenses
        let message = "Share this link with your friends to earn rewards when they join!";

        if (data.referralActive && data.referralSettings) {
          const referrerReward = data.referralSettings.referrerReward;
          const referredReward = data.referralSettings.referredReward;

          if (referrerReward && referredReward) {
            if (referrerReward.type === 'points' && referredReward.type === 'points') {
              message = `Share this link! You earn ${referrerReward.amount} points and your friend gets ${referredReward.amount} points when they join.`;
            }
          }
        }

        this.referralDescription.textContent = message;
      }
    } catch (error) {
      console.error('Erreur lors de la mise à jour de la description de parrainage:', error);
    }
  }

  async updateReferralCount() {
    try {
      if (!this.customerData || !this.options.customerId) {
        return;
      }

      const response = await fetch(
        `${this.apiBaseUrl}?prepath=customer-referrals&shop=${this.options.shop}&customerId=${this.options.customerId}`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      if (response.ok) {
        const data = await response.json();
        const completedReferrals = data.referrals?.filter(r => r.status === 'completed').length || 0;

        if (this.referralCount) {
          this.referralCount.textContent = `${completedReferrals} referrals completed`;
        }
      }
    } catch (error) {
      console.error('Erreur lors de la mise à jour du compteur de parrainages:', error);
    }
  }

  copyReferralLink() {
    if (this.referralLink) {
      this.referralLink.select();
      this.referralLink.setSelectionRange(0, 99999);
      navigator.clipboard.writeText(this.referralLink.value).then(() => {
        // Feedback visuel
        const originalText = this.copyReferralBtn.innerHTML;
        this.copyReferralBtn.innerHTML = '✓';
        setTimeout(() => {
          this.copyReferralBtn.innerHTML = originalText;
        }, 1000);
      });
    }
  }

  shareOnFacebook() {
    const url = encodeURIComponent(this.referralData?.url || this.referralLink?.value || '');
    const programName = this.programInfo?.name || 'programme de fidélité';
    const text = encodeURIComponent(`Rejoignez-moi sur ${programName} et gagnez des récompenses !`);
    window.open(`https://www.facebook.com/sharer/sharer.php?u=${url}&quote=${text}`, '_blank');
  }

  shareOnTwitter() {
    const url = encodeURIComponent(this.referralData?.url || this.referralLink?.value || '');
    const programName = this.programInfo?.name || 'programme de fidélité';
    const text = encodeURIComponent(`Rejoignez-moi sur ${programName} et gagnez des récompenses ! ${url}`);
    window.open(`https://twitter.com/intent/tweet?text=${text}`, '_blank');
  }

  shareByEmail() {
    const url = this.referralData?.url || this.referralLink?.value || '';
    const programName = this.programInfo?.name || 'programme de fidélité';
    const subject = encodeURIComponent(`Invitation au ${programName}`);
    const body = encodeURIComponent(`Salut ! Je t'invite à rejoindre ${programName}. Utilise ce lien pour bénéficier de récompenses : ${url}`);
    window.open(`mailto:?subject=${subject}&body=${body}`, '_blank');
  }

  async redeemProduct(productId) {
    if (!this.customerData || !this.options.customerId) {
      alert('Vous devez être connecté pour échanger des points.');
      return;
    }

    const product = this.exchangeableProducts.find(p => p.id === productId);
    if (!product) return;

    if (this.customerData.points < product.pointsCost) {
      alert(`Vous n'avez pas assez de points. Il vous manque ${product.pointsCost - this.customerData.points} points.`);
      return;
    }

    if (!confirm(`Êtes-vous sûr de vouloir échanger ${product.pointsCost} points contre "${product.title}" ?`)) {
      return;
    }

    try {
      const response = await fetch(
        `${this.apiBaseUrl}?prepath=client-redeem`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
          body: new URLSearchParams({
            shop: this.options.shop,
            customerId: this.options.customerId,
            productId: productId
          })
        }
      );

      const result = await response.json();

      if (response.ok && result.success) {
        // Mettre à jour les points du client
        this.customerData.points = result.remainingPoints;
        this.updateCustomerInfo();
        this.updatePointsPreview();
        this.renderWaysToRedeem(); // Rafraîchir la liste

        alert(`🎉 ${result.message}\nPoints restants: ${result.remainingPoints}`);
      } else {
        alert(`Erreur: ${result.error || 'Impossible d\'échanger le produit'}`);
      }
    } catch (error) {
      console.error('Erreur lors de l\'échange:', error);
      alert('Une erreur est survenue lors de l\'échange. Veuillez réessayer.');
    }
  }

  // ===== NOUVELLES MÉTHODES POUR LES COUPONS =====

  showCouponConfigSection(wayToRedeem) {
    this.currentWayToRedeem = wayToRedeem;
    this.hideAllSections();
    this.couponConfigSection.style.display = 'block';

    // Mettre à jour l'en-tête avec les points actuels
    this.customerPointsHeader.textContent = `${formatCompactNumber(this.customerData.points)} Points`;

    // Configurer les limites d'input
    const minPoints = wayToRedeem.minPoints || 100;
    const maxPoints = Math.min(wayToRedeem.maxPoints || 10000, this.customerData.points);

    this.pointsInput.min = minPoints;
    this.pointsInput.max = maxPoints;
    this.pointsInput.value = minPoints;
    this.pointsInput.step = 100;

    // Mettre à jour la valeur initiale
    this.updateCouponValue();
  }

  showCouponResultSection(couponData) {
    this.hideAllSections();
    this.couponResultSection.style.display = 'block';

    // Mettre à jour l'en-tête avec les nouveaux points
    this.customerPointsResult.textContent = `${formatCompactNumber(this.customerData.points)} Points`;

    // Afficher les détails du coupon
    document.getElementById('generated-coupon-title').textContent = `€${couponData.value} off coupon`;
    document.getElementById('generated-coupon-subtitle').textContent = `Spent ${couponData.pointsSpent} Points`;
    this.generatedCouponCode.value = couponData.code;

    // Stocker le code pour l'application au panier
    this.currentCouponCode = couponData.code;
  }

  // ===== MÉTHODES POUR LES RÉCOMPENSES =====

  /**
   * Afficher la page de détail d'une récompense disponible
   */
  async useReward(rewardId) {
    try {
      // Récupérer les détails de la récompense
      const response = await fetch(
        `${this.apiBaseUrl}?prepath=customer-rewards&shop=${this.options.shop}&customerId=${this.customerData?.customerId || this.options.customerId}`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      if (response.ok) {
        const data = await response.json();
        const reward = data.availableRewards?.find(r => r.id === rewardId);

        if (!reward) {
          alert('Reward not found or no longer available');
          return;
        }

        // Afficher la page de détail de la récompense
        this.showRewardDetailSection(reward);
      } else {
        throw new Error('Failed to fetch reward details');
      }
    } catch (error) {
      console.error('Erreur lors de l\'affichage de la récompense:', error);
      alert('Unable to display reward details. Please try again later.');
    }
  }

  /**
   * Afficher la section de détail d'une récompense avec le code
   */
  showRewardDetailSection(reward) {
    // Vérifier que panelBody existe
    if (!this.panelBody) {
      console.error('Panel body not found');
      return;
    }

    // Masquer toutes les sections
    this.hideAllSections();

    // Masquer le header du membre
    if (this.memberHeader) {
      this.memberHeader.style.display = 'none';
    }

    // Créer la section de détail
    const detailSection = document.createElement('div');
    detailSection.className = 'loyalty-reward-detail-section';
    detailSection.innerHTML = `
      <div class="loyalty-section-header">
        <button class="loyalty-back-btn" id="back-from-reward-detail">
          <span class="loyalty-back-icon">←</span>
          <span id="customer-points-header-detail">${formatCompactNumber(this.customerData?.points || 0)} Points</span>
        </button>
      </div>
      <div class="loyalty-section-content">
        <div class="loyalty-reward-detail-header">
          <div class="loyalty-reward-detail-icon">🎫</div>
          <h5>${reward.title}</h5>
          <p class="loyalty-reward-detail-subtitle">${reward.subtitle}</p>
        </div>

        <div class="loyalty-reward-detail-instructions">
          <p>Use this discount code on your next order!</p>
        </div>

        <div class="loyalty-reward-detail-code-section">
          <div class="loyalty-reward-detail-code-display">
            <input
              type="text"
              value="${reward.code}"
              readonly
              class="loyalty-reward-detail-code-input"
              id="reward-detail-code"
            />
            <button class="loyalty-reward-detail-copy-btn" onclick="loyaltyWidget.copyRewardCode()">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
              </svg>
            </button>

          </div>

          <button class="loyalty-reward-detail-apply-btn" onclick="loyaltyWidget.applyRewardCode('${reward.code}')">
            Apply code
          </button>
        </div>
      </div>
    `;

    // Ajouter la section au panel
    this.panelBody.appendChild(detailSection);

    // Event listener pour le bouton de retour
    const backBtn = document.getElementById('back-from-reward-detail');
    backBtn.addEventListener('click', () => {
      console.log("clicked");
      this.panelBody.removeChild(detailSection);
      this.showYourRewardsSection();
    });

    // Animation d'entrée
    setTimeout(() => {
      detailSection.classList.add('loyalty-section-slide-in');
    }, 10);

    this.currentView = 'reward-detail';
    this.currentRewardDetailSection = detailSection;
  }

  /**
   * Copier le code de récompense
   */
  copyRewardCode() {
    const codeInput = document.getElementById('reward-detail-code');
    if (codeInput) {
      codeInput.select();
      document.execCommand('copy');

      // Feedback visuel
      const copyBtn = document.querySelector('.loyalty-reward-detail-copy-btn');
      if (copyBtn) {
        const originalText = copyBtn.innerHTML;
        copyBtn.innerHTML = '✅';
        copyBtn.style.background = '#4caf50';
        setTimeout(() => {
          copyBtn.innerHTML = originalText;
          copyBtn.style.background = '';
        }, 2000);
      }
    }
  }

  /**
   * Appliquer le code de récompense et rediriger
   */
  applyRewardCode(code) {
    window.location.href = `/checkout?discount=${code}`;
  }

  // ===== MÉTHODES POUR LES SECTIONS DE COUPONS =====

  handleRedeemClick(wayToRedeemId) {
    const wayToRedeem = this.redeemWays.find(way => way.id === wayToRedeemId);
    if (!wayToRedeem) return;
    console.log("wayToRedeem = ", wayToRedeem);
    if (wayToRedeem.redeemType === 'coupon' && wayToRedeem.isConfigurable) {
      // Coupon configurable - afficher la section avec slider
      this.showConfigurableCouponSection(wayToRedeem);
    } else {
      // Tous les autres types (coupon fixe, discount, product, shipping) - utiliser le système unifié
      this.handleUnifiedRedemption(wayToRedeem);
    }
  }

  async updateCouponValue() {
    const points = parseInt(this.pointsInput.value) || 0;
    if (points <= 0) {
      this.couponValueDisplay.textContent = '€0';
      return;
    }

    try {
      const response = await fetch(
        `${this.apiBaseUrl}?prepath=coupon-value&shop=${this.options.shop}&points=${points}`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      if (response.ok) {
        const data = await response.json();
        this.couponValueDisplay.textContent = data.formatted;

        // Mettre à jour le titre et sous-titre
        document.getElementById('coupon-config-title').textContent = `${data.formatted} off coupon`;
        document.getElementById('coupon-config-subtitle').textContent = `Spent ${formatCompactNumber(points)} Points`;
      }
    } catch (error) {
      console.error('Erreur lors du calcul de la valeur du coupon:', error);
    }
  }

  async createCoupon() {
    const pointsToSpend = parseInt(this.pointsInput.value) || 0;

    if (pointsToSpend <= 0) {
      alert('Veuillez entrer un nombre de points valide.');
      return;
    }

    if (pointsToSpend > this.customerData.points) {
      alert('Vous n\'avez pas assez de points.');
      return;
    }

    try {
      this.redeemCouponBtn.disabled = true;
      this.redeemCouponBtn.textContent = 'Création...';

      const response = await fetch(
        `${this.apiBaseUrl}?prepath=create-coupon`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
          body: new URLSearchParams({
            shop: this.options.shop,
            customerId: this.options.customerId,
            pointsToSpend: pointsToSpend.toString(),
            wayToRedeemId: this.currentWayToRedeem?.id || ''
          })
        }
      );

      const result = await response.json();

      if (response.ok && result.success) {
        // Mettre à jour les points du client
        this.customerData.points -= pointsToSpend;
        this.updateCustomerInfo();
        this.updatePointsPreview();

        // Afficher la page de résultat
        this.showCouponResultSection(result.coupon);
      } else {
        alert(`Erreur: ${result.error || 'Impossible de créer le coupon'}`);
      }
    } catch (error) {
      console.error('Erreur lors de la création du coupon:', error);
      alert('Une erreur est survenue lors de la création du coupon. Veuillez réessayer.');
    } finally {
      this.redeemCouponBtn.disabled = false;
      this.redeemCouponBtn.textContent = 'Redeem';
    }
  }





  /**
   * Gérer l'échange unifié avec modal de confirmation élégante
   */
  async handleUnifiedRedemption(wayToRedeem) {
    // Vérifier si le client a assez de points
    if (!this.customerData || this.customerData.points < wayToRedeem.pointsCost) {
      const pointsNeeded = wayToRedeem.pointsCost - (this.customerData?.points || 0);
      alert(`Points insuffisants. Il vous manque ${pointsNeeded} points pour cette récompense.`);
      return;
    }

    // Afficher la modal de confirmation élégante
    const confirmModal = document.createElement('div');
    confirmModal.className = 'loyalty-confirm-modal';

    // Déterminer l'icône selon le type
    let icon = '🎫';
    if (wayToRedeem.redeemType === 'discount') icon = '💰';
    if (wayToRedeem.redeemType === 'product') icon = '🎁';
    if (wayToRedeem.redeemType === 'shipping') icon = '🚚';

    // Déterminer la description de la récompense
    let rewardDescription = wayToRedeem.name;
    if (wayToRedeem.redeemType === 'coupon') {
      rewardDescription = `€${wayToRedeem.redeemValue} discount coupon`;
    } else if (wayToRedeem.redeemType === 'discount') {
      rewardDescription = `€${wayToRedeem.redeemValue} discount`;
    } else if (wayToRedeem.redeemType === 'shipping') {
      rewardDescription = 'free shipping';
    }

    confirmModal.innerHTML = `
      <div class="loyalty-modal-overlay">
        <div class="loyalty-modal-content">
          <div class="loyalty-confirm-header">
            <div class="loyalty-confirm-icon">${icon}</div>
            <h3>Confirm Redemption</h3>
          </div>
          <div class="loyalty-confirm-body">
            <p>You are about to redeem <strong>${wayToRedeem.pointsCost} points</strong> for <strong>${rewardDescription}</strong>.</p>
            <p>Are you sure you want to continue?</p>
          </div>
          <div class="loyalty-confirm-footer">
            <button class="loyalty-btn-secondary" id="cancel-unified-redeem">Cancel</button>
            <button class="loyalty-btn-primary" id="confirm-unified-redeem">Continue</button>
          </div>
        </div>
      </div>
    `;

    document.body.appendChild(confirmModal);

    // Event listeners pour la modal
    const cancelBtn = document.getElementById('cancel-unified-redeem');
    const confirmBtn = document.getElementById('confirm-unified-redeem');

    cancelBtn.addEventListener('click', () => {
      document.body.removeChild(confirmModal);
    });

    confirmBtn.addEventListener('click', async () => {
      // Remplacer le contenu par un loader
      confirmModal.querySelector('.loyalty-modal-content').innerHTML = `
        <div class="loyalty-loading-content">
          <div class="loyalty-spinner"></div>
          <p>Processing your redemption...</p>
        </div>
      `;

      try {
        // Appeler l'API d'échange selon le type
        let response;

        switch (wayToRedeem.redeemType) {
          case 'coupon':
            response = await this.redeemCoupon(wayToRedeem);
            break;
          case 'discount':
            response = await this.redeemDiscount(wayToRedeem);
            break;
          case 'product':
            response = await this.redeemProduct(wayToRedeem);
            break;
          case 'shipping':
            response = await this.redeemFreeShipping(wayToRedeem);
            break;
          default:
            throw new Error(`Type de récompense "${wayToRedeem.redeemType}" non supporté.`);
        }

        if (response && response.success) {
          // Fermer la modal de confirmation
          document.body.removeChild(confirmModal);

          // Mettre à jour les points du client
          this.customerData.points -= wayToRedeem.pointsCost;
          this.updateCustomerInfo();
          this.updatePointsPreview();
          this.renderWaysToRedeem(); // Rafraîchir la liste
          this.renderWaysToRedeemMain(); // Rafraîchir la section principale

          // Afficher le succès avec le code généré
          this.showRedemptionSuccess(response, wayToRedeem);
        } else {
          throw new Error(response?.error || 'Impossible d\'échanger cette récompense');
        }
      } catch (error) {
        console.error('Erreur lors de l\'échange:', error);
        document.body.removeChild(confirmModal);
        alert('Une erreur est survenue lors de l\'échange. Veuillez réessayer.');
      }
    });

    // Animation d'apparition
    setTimeout(() => {
      confirmModal.classList.add('active');
    }, 10);
  }

  /**
   * Échanger des points contre un coupon fixe
   */
  async redeemCoupon(wayToRedeem) {
    try {
      const formData = new FormData();
      formData.append('customerId', this.customerData.customerId);
      formData.append('wayToRedeemId', wayToRedeem.id);
      formData.append('pointsToSpend', wayToRedeem.pointsCost.toString());
      formData.append('redeemType', 'coupon');
      formData.append('redeemValue', wayToRedeem.redeemValue.toString());

      const response = await fetch(
        `${this.apiBaseUrl}?prepath=client-redeem&shop=${this.options.shop}`,
        {
          method: 'POST',
          body: formData
        }
      );

      const result = await response.json();
      return result;
    } catch (error) {
      console.error('Erreur lors de l\'échange de coupon:', error);
      return { success: false, error: 'Erreur de communication' };
    }
  }

  /**
   * Échanger des points contre une réduction fixe
   */
  async redeemDiscount(wayToRedeem) {
    try {
      const formData = new FormData();
      formData.append('customerId', this.customerData.customerId);
      formData.append('wayToRedeemId', wayToRedeem.id);
      formData.append('pointsToSpend', wayToRedeem.pointsCost.toString());
      formData.append('redeemType', 'discount');
      formData.append('redeemValue', wayToRedeem.redeemValue.toString());

      const response = await fetch(
        `${this.apiBaseUrl}?prepath=client-redeem&shop=${this.options.shop}`,
        {
          method: 'POST',
          body: formData
        }
      );

      const result = await response.json();
      return result;
    } catch (error) {
      console.error('Erreur lors de l\'échange de réduction:', error);
      return { success: false, error: 'Erreur de communication' };
    }
  }

  /**
   * Échanger des points contre un produit gratuit
   */
  async redeemProduct(wayToRedeem) {
    try {
      const formData = new FormData();
      formData.append('customerId', this.customerData.customerId);
      formData.append('wayToRedeemId', wayToRedeem.id);
      formData.append('pointsToSpend', wayToRedeem.pointsCost.toString());
      formData.append('redeemType', 'product');

      const response = await fetch(
        `${this.apiBaseUrl}?prepath=client-redeem&shop=${this.options.shop}`,
        {
          method: 'POST',
          body: formData
        }
      );

      const result = await response.json();
      return result;
    } catch (error) {
      console.error('Erreur lors de l\'échange de produit:', error);
      return { success: false, error: 'Erreur de communication' };
    }
  }

  /**
   * Échanger des points contre la livraison gratuite
   */
  async redeemFreeShipping(wayToRedeem) {
    try {
      const formData = new FormData();
      formData.append('customerId', this.customerData.customerId);
      formData.append('wayToRedeemId', wayToRedeem.id);
      formData.append('pointsToSpend', wayToRedeem.pointsCost.toString());
      formData.append('redeemType', 'shipping');

      const response = await fetch(
        `${this.apiBaseUrl}?prepath=client-redeem&shop=${this.options.shop}`,
        {
          method: 'POST',
          body: formData
        }
      );

      const result = await response.json();
      return result;
    } catch (error) {
      console.error('Erreur lors de l\'échange de livraison gratuite:', error);
      return { success: false, error: 'Erreur de communication' };
    }
  }

  /**
   * Afficher le succès de l'échange avec le code généré
   */
  showRedemptionSuccess(response, wayToRedeem) {
    const code = response.code || response.discountCode;

    if (code) {
      // Créer une modal de succès
      const modal = document.createElement('div');
      modal.className = 'loyalty-redemption-success-modal';
      modal.innerHTML = `
        <div class="loyalty-modal-overlay">
          <div class="loyalty-modal-content">
            <div class="loyalty-success-header">
              <div class="loyalty-success-icon">✅</div>
              <h3>Échange réussi !</h3>
            </div>
            <div class="loyalty-success-body">
              <p>Votre code de réduction a été généré :</p>
              <div class="loyalty-code-display">
                <span class="loyalty-code">${code}</span>
                <button class="loyalty-copy-btn" onclick="loyaltyWidget.copyToClipboard('${code}')">
                  📋 Copier
                </button>
              </div>
              <p class="loyalty-code-instructions">
                Utilisez ce code lors de votre prochaine commande pour bénéficier de votre récompense.
              </p>
            </div>
            <div class="loyalty-success-footer">
              <button class="loyalty-btn-primary" onclick="loyaltyWidget.closeRedemptionModal()">
                Fermer
              </button>
              <button class="loyalty-btn-secondary" onclick="loyaltyWidget.applyCodeAndCheckout('${code}')">
                Utiliser maintenant
              </button>
            </div>
          </div>
        </div>
      `;

      document.body.appendChild(modal);
      this.currentRedemptionModal = modal;

      // Animation d'apparition
      setTimeout(() => {
        modal.classList.add('active');
      }, 10);
    } else {
      alert(`Échange réussi ! ${response.message || 'Votre récompense a été activée.'}`);
    }
  }

  /**
   * Afficher la section de configuration de coupon avec slider
   */
  showConfigurableCouponSection(wayToRedeem) {
    // Vérifier que panelBody existe
    if (!this.panelBody) {
      console.error('Panel body not found');
      return;
    }

    // Masquer toutes les sections
    this.hideAllSections();

    // Masquer le header du membre
    if (this.memberHeader) {
      this.memberHeader.style.display = 'none';
    }

    // Créer la section de configuration
    const configSection = document.createElement('div');
    configSection.className = 'loyalty-configurable-coupon-section';
    configSection.innerHTML = `
      <div class="loyalty-section-header">
        <button class="loyalty-back-btn" id="back-from-configurable-coupon">
          <span class="loyalty-back-icon">←</span>
          <span id="customer-points-header-config">${formatCompactNumber(this.customerData?.points || 0)} Points</span>
        </button>
      </div>
      <div class="loyalty-section-content">
        <div class="loyalty-coupon-config-header">
          <div class="loyalty-coupon-icon">🎫</div>
          <h5>${wayToRedeem.name}</h5>
          <p class="loyalty-coupon-rate">${this.programInfo?.redemptionRate || 100} Points = €1</p>
        </div>

        <div class="loyalty-coupon-calculator">
          <div class="loyalty-points-display">
            <span id="selected-points">${wayToRedeem.minPoints || 100}</span> Points for
            <span id="coupon-value">€${((wayToRedeem.minPoints || 100) / (this.programInfo?.redemptionRate || 100)).toFixed(2)}</span> off
          </div>

          <div class="loyalty-points-slider-container">
            <input
              type="range"
              id="points-slider"
              min="${wayToRedeem.minPoints || 100}"
              max="${Math.min(wayToRedeem.maxPoints || 10000, this.customerData?.points || 0)}"
              value="${wayToRedeem.minPoints || 100}"
              step="50"
              class="loyalty-points-slider"
            />
            <div class="loyalty-slider-labels">
              <span>${wayToRedeem.minPoints || 100}</span>
              <span>${Math.min(wayToRedeem.maxPoints || 10000, this.customerData?.points || 0)}</span>
            </div>
          </div>

          <button class="loyalty-redeem-btn-primary" id="confirm-coupon-redeem">
            Redeem
          </button>
        </div>
      </div>
    `;

    // Ajouter la section au panel
    this.panelBody.appendChild(configSection);

    // Ajouter les event listeners
    const backBtn = document.getElementById('back-from-configurable-coupon');
    const slider = document.getElementById('points-slider');
    const redeemBtn = document.getElementById('confirm-coupon-redeem');
    const selectedPointsSpan = document.getElementById('selected-points');
    const couponValueSpan = document.getElementById('coupon-value');

    backBtn.addEventListener('click', () => {
      this.panelBody.removeChild(configSection);
      this.showMainSection();
    });

    slider.addEventListener('input', (e) => {
      const points = parseInt(e.target.value);
      const redemptionRate = this.programInfo?.redemptionRate || 100;
      const value = (points / redemptionRate).toFixed(2);
      selectedPointsSpan.textContent = points;
      couponValueSpan.textContent = `€${value}`;
    });

    redeemBtn.addEventListener('click', () => {
      const pointsToSpend = parseInt(slider.value);
      this.confirmConfigurableCouponRedeem(wayToRedeem, pointsToSpend, configSection);
    });

    // Animation d'entrée
    setTimeout(() => {
      configSection.classList.add('loyalty-section-slide-in');
    }, 10);

    this.currentView = 'configurable-coupon';
  }

  /**
   * Confirmer l'échange de coupon configurable
   */
  async confirmConfigurableCouponRedeem(wayToRedeem, pointsToSpend, configSection) {
    // Vérifier les points disponibles
    if (pointsToSpend > this.customerData.points) {
      alert(`Points insuffisants. Vous avez ${this.customerData.points} points disponibles.`);
      return;
    }

    // Afficher la modal de confirmation
    const confirmModal = document.createElement('div');
    confirmModal.className = 'loyalty-confirm-modal';
    confirmModal.innerHTML = `
      <div class="loyalty-modal-overlay">
        <div class="loyalty-modal-content">
          <div class="loyalty-confirm-header">
            <div class="loyalty-confirm-icon">🎫</div>
            <h3>Confirm Redemption</h3>
          </div>
          <div class="loyalty-confirm-body">
            <p>You are about to redeem <strong>${pointsToSpend} points</strong> for a <strong>€${(pointsToSpend / (this.programInfo?.redemptionRate || 100)).toFixed(2)} discount coupon</strong>.</p>
            <p>Are you sure you want to continue?</p>
          </div>
          <div class="loyalty-confirm-footer">
            <button class="loyalty-btn-secondary" id="cancel-redeem">Cancel</button>
            <button class="loyalty-btn-primary" id="confirm-redeem">Continue</button>
          </div>
        </div>
      </div>
    `;

    document.body.appendChild(confirmModal);

    // Event listeners pour la modal
    const cancelBtn = document.getElementById('cancel-redeem');
    const confirmBtn = document.getElementById('confirm-redeem');

    cancelBtn.addEventListener('click', () => {
      document.body.removeChild(confirmModal);
    });

    confirmBtn.addEventListener('click', async () => {
      // Remplacer le contenu par un loader
      confirmModal.querySelector('.loyalty-modal-content').innerHTML = `
        <div class="loyalty-loading-content">
          <div class="loyalty-spinner"></div>
          <p>Processing your redemption...</p>
        </div>
      `;

      try {
        // Appeler l'API pour créer le coupon
        const result = await this.createConfigurableCoupon(wayToRedeem, pointsToSpend);

        if (result.success) {
          // Fermer la modal de confirmation
          document.body.removeChild(confirmModal);

          // Supprimer la section de configuration
          this.panelBody.removeChild(configSection);

          // Mettre à jour les points du client
          this.customerData.points -= pointsToSpend;
          this.updateCustomerInfo();
          this.updatePointsPreview();

          // Afficher la section de résultat
          this.showCouponResultSection(result, pointsToSpend);
        } else {
          throw new Error(result.error || 'Erreur lors de la création du coupon');
        }
      } catch (error) {
        console.error('Erreur lors de l\'échange:', error);
        document.body.removeChild(confirmModal);
        alert('Une erreur est survenue lors de l\'échange. Veuillez réessayer.');
      }
    });

    // Animation d'apparition
    setTimeout(() => {
      confirmModal.classList.add('active');
    }, 10);
  }

  /**
   * Copier le code dans le presse-papiers
   */
  copyToClipboard(code) {
    navigator.clipboard.writeText(code).then(() => {
      // Feedback visuel
      const copyBtn = document.querySelector('.loyalty-copy-btn');
      if (copyBtn) {
        const originalText = copyBtn.innerHTML;
        copyBtn.innerHTML = '✅ Copié !';
        copyBtn.style.background = '#4caf50';
        setTimeout(() => {
          copyBtn.innerHTML = originalText;
          copyBtn.style.background = '';
        }, 2000);
      }
    }).catch(() => {
      // Fallback pour les navigateurs plus anciens
      const textArea = document.createElement('textarea');
      textArea.value = code;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      alert('Code copié dans le presse-papiers !');
    });
  }

  /**
   * Fermer la modal de succès
   */
  closeRedemptionModal() {
    if (this.currentRedemptionModal) {
      this.currentRedemptionModal.classList.remove('active');
      setTimeout(() => {
        document.body.removeChild(this.currentRedemptionModal);
        this.currentRedemptionModal = null;
      }, 300);
    }
  }

  /**
   * Créer un coupon configurable
   */
  async createConfigurableCoupon(wayToRedeem, pointsToSpend) {
    try {
      const formData = new FormData();
      formData.append('customerId', this.customerData.customerId);
      formData.append('wayToRedeemId', wayToRedeem.id);
      formData.append('pointsToSpend', pointsToSpend.toString());
      formData.append('redeemType', 'coupon');
      formData.append('redeemValue', (pointsToSpend / 100).toString()); // 100 points = 1€

      const response = await fetch(
        `${this.apiBaseUrl}?prepath=client-redeem&shop=${this.options.shop}`,
        {
          method: 'POST',
          body: formData
        }
      );

      const result = await response.json();
      return result;
    } catch (error) {
      console.error('Erreur lors de la création du coupon configurable:', error);
      return { success: false, error: 'Erreur de communication' };
    }
  }

  /**
   * Afficher la section de résultat avec le code généré
   */
  showCouponResultSection(result, pointsSpent) {
    // Vérifier que panelBody existe
    if (!this.panelBody) {
      console.error('Panel body not found');
      return;
    }

    const resultSection = document.createElement('div');
    resultSection.className = 'loyalty-coupon-result-section';
    resultSection.innerHTML = `
      <div class="loyalty-section-header">
        <button class="loyalty-back-btn" id="back-from-coupon-result">
          <span class="loyalty-back-icon">←</span>
          <span id="customer-points-header-result">${formatCompactNumber(this.customerData?.points || 0)} Points</span>
        </button>
      </div>
      <div class="loyalty-section-content">
        <div class="loyalty-coupon-success-header">
          <div class="loyalty-coupon-icon">🎫</div>
          <h5>€${(pointsSpent / (this.programInfo?.redemptionRate || 100)).toFixed(2)} off coupon</h5>
          <p class="loyalty-coupon-spent">Spent ${pointsSpent} Points</p>
        </div>

        <div class="loyalty-coupon-instructions">
          <p>Use this discount code on your next order!</p>
        </div>

        <div class="loyalty-coupon-code-section">
          <div class="loyalty-coupon-code-display">
            <input
              type="text"
              value="${result.code}"
              readonly
              class="loyalty-coupon-code-input"
              id="generated-coupon-code"
            />
            <button class="loyalty-reward-detail-copy-btn" onclick="loyaltyWidget.copyRewardCode()">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
              </svg>
            </button>
          </div>

          <button class="loyalty-apply-code-btn" onclick="loyaltyWidget.applyGeneratedCode('${result.code}')">
            Apply code
          </button>
        </div>
      </div>
    `;

    // Ajouter la section au panel
    this.panelBody.appendChild(resultSection);

    // Event listener pour le bouton de retour
    const backBtn = document.getElementById('back-from-coupon-result');
    backBtn.addEventListener('click', () => {
      this.panelBody.removeChild(resultSection);
      this.showMainSection();
    });

    // Animation d'entrée
    setTimeout(() => {
      resultSection.classList.add('loyalty-section-slide-in');
    }, 10);

    this.currentView = 'coupon-result';
    this.currentResultSection = resultSection;
  }

  /**
   * Copier le code généré
   */
  copyGeneratedCode() {
    const codeInput = document.getElementById('generated-coupon-code');
    if (codeInput) {
      codeInput.select();
      document.execCommand('copy');

      // Feedback visuel
      const copyBtn = document.querySelector('.loyalty-copy-code-btn');
      if (copyBtn) {
        const originalText = copyBtn.innerHTML;
        copyBtn.innerHTML = '✅';
        copyBtn.style.background = '#4caf50';
        setTimeout(() => {
          copyBtn.innerHTML = originalText;
          copyBtn.style.background = '';
        }, 2000);
      }
    }
  }

  /**
   * Appliquer le code généré et rediriger
   */
  applyGeneratedCode(code) {
    window.location.href = `/checkout?discount=${code}`;
  }

  /**
   * Appliquer le code et rediriger vers le checkout
   */
  applyCodeAndCheckout(code) {
    this.closeRedemptionModal();
    // Rediriger vers le checkout avec le code appliqué
    window.location.href = `/checkout?discount=${code}`;
  }

  open() {
    if (this.isOpen) return;

    this.isOpen = true;
    this.panel.classList.add('active');
    this.overlay.classList.add('active');
    document.body.style.overflow = 'hidden';

    // Animation du bouton
    this.trigger.style.transform = 'scale(0.9)';
    setTimeout(() => {
      this.trigger.style.transform = '';
    }, 150);
  }

  close() {
    if (!this.isOpen) return;

    this.isOpen = false;
    this.panel.classList.remove('active');
    this.overlay.classList.remove('active');
    document.body.style.overflow = '';
  }

  toggle() {
    if (this.isOpen) {
      this.close();
    } else {
      this.open();
    }
  }

  openRewards() {
    this.currentView = 'rewards';
    this.renderRewardsView();
  }

  openHistory() {
    this.currentView = 'history';
    this.renderHistoryView();
  }

  backToMain() {
    this.currentView = 'main';
    this.renderMainView();
  }

  renderMainView() {
    // Afficher le contenu principal et masquer les autres vues
    const mainContent = this.panel.querySelector('.loyalty-main-content');
    const rewardsContent = this.panel.querySelector('.loyalty-rewards-content');
    const historyContent = this.panel.querySelector('.loyalty-history-content');

    if (mainContent) mainContent.style.display = 'block';
    if (rewardsContent) rewardsContent.style.display = 'none';
    if (historyContent) historyContent.style.display = 'none';
  }

  renderRewardsView() {
    // Masquer le contenu principal et afficher les récompenses
    const mainContent = this.panel.querySelector('.loyalty-main-content');
    const rewardsContent = this.panel.querySelector('.loyalty-rewards-content');
    const historyContent = this.panel.querySelector('.loyalty-history-content');

    if (mainContent) mainContent.style.display = 'none';
    if (historyContent) historyContent.style.display = 'none';

    if (!rewardsContent) {
      this.createRewardsView();
    } else {
      rewardsContent.style.display = 'block';
    }
  }

  renderHistoryView() {
    // Masquer le contenu principal et afficher l'historique
    const mainContent = this.panel.querySelector('.loyalty-main-content');
    const rewardsContent = this.panel.querySelector('.loyalty-rewards-content');
    const historyContent = this.panel.querySelector('.loyalty-history-content');

    if (mainContent) mainContent.style.display = 'none';
    if (rewardsContent) rewardsContent.style.display = 'none';

    if (!historyContent) {
      this.createHistoryView();
    } else {
      historyContent.style.display = 'block';
    }
  }

  createRewardsView() {
    const rewardsContent = document.createElement('div');
    rewardsContent.className = 'loyalty-rewards-content';
    rewardsContent.innerHTML = `
      <div class="loyalty-view-header">
        <button class="loyalty-back-btn" onclick="loyaltyWidget.backToMain()">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M19 12H5M12 19L5 12L12 5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
          Retour
        </button>
        <h3>Récompenses disponibles</h3>
      </div>
      <div class="loyalty-rewards-list">
        ${this.renderRewardsList()}
      </div>
    `;

    this.panel.querySelector('.loyalty-content').appendChild(rewardsContent);
  }

  createHistoryView() {
    const historyContent = document.createElement('div');
    historyContent.className = 'loyalty-history-content';
    historyContent.innerHTML = `
      <div class="loyalty-view-header">
        <button class="loyalty-back-btn" onclick="loyaltyWidget.backToMain()">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M19 12H5M12 19L5 12L12 5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
          Retour
        </button>
        <h3>Historique des points</h3>
      </div>
      <div class="loyalty-history-list">
        ${this.renderHistoryList()}
      </div>
    `;

    this.panel.querySelector('.loyalty-content').appendChild(historyContent);
  }

  renderRewardsList() {
    if (!this.redeemWays.length) {
      return '<div class="loyalty-empty-state">Aucune récompense disponible pour le moment.</div>';
    }

    const currentPoints = this.customerData?.points || 0;

    return this.redeemWays.map(reward => {
      const canAfford = currentPoints >= reward.pointsCost;
      const statusClass = canAfford ? 'available' : 'unavailable';

      return `
        <div class="loyalty-reward-item ${statusClass}">
          <div class="loyalty-reward-icon">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 2L15.09 8.26L22 9L17 14L18.18 21L12 17.77L5.82 21L7 14L2 9L8.91 8.26L12 2Z" fill="currentColor"/>
            </svg>
          </div>
          <div class="loyalty-reward-content">
            <div class="loyalty-reward-title">${reward.name}</div>
            <div class="loyalty-reward-description">${reward.description}</div>
            <div class="loyalty-reward-cost">${formatCompactNumber(reward.pointsCost)} points</div>
          </div>
          <div class="loyalty-reward-action">
            ${canAfford
              ? '<button class="loyalty-btn loyalty-btn-primary loyalty-btn-sm">Échanger</button>'
              : `<span class="loyalty-points-needed">Il vous manque ${formatCompactNumber(reward.pointsCost - currentPoints)} points</span>`
            }
          </div>
        </div>
      `;
    }).join('');
  }

  renderHistoryList() {
    const history = this.customerData?.recentHistory || [];

    if (!history.length) {
      return '<div class="loyalty-empty-state">Aucun historique disponible.</div>';
    }

    return history.map(item => {
      const isPositive = item.points > 0;
      const pointsClass = isPositive ? 'positive' : 'negative';
      const pointsPrefix = isPositive ? '+' : '';

      return `
        <div class="loyalty-history-item">
          <div class="loyalty-history-icon ${pointsClass}">
            ${isPositive
              ? '<svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M12 5V19M5 12L19 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>'
              : '<svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M5 12H19" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>'
            }
          </div>
          <div class="loyalty-history-content">
            <div class="loyalty-history-description">${item.description}</div>
            <div class="loyalty-history-date">${new Date(item.timestamp).toLocaleDateString('fr-FR')}</div>
          </div>
          <div class="loyalty-history-points ${pointsClass}">
            ${pointsPrefix}${formatCompactNumber(item.points)}
          </div>
        </div>
      `;
    }).join('');
  }

  async signupToLoyalty() {
    if (!this.options.customerId) {
      console.error('Customer ID required for signup');
      return;
    }

    try {
      // Récupérer le code de parrainage stocké
      const referralCode = this.getReferralCode();

      // Construire l'URL avec le code de parrainage si disponible
      let signupUrl = `${this.apiBaseUrl}?prepath=signup&shop=${this.options.shop}&logged_in_customer_id=${this.options.customerId}`;
      if (referralCode) {
        signupUrl += `&referralCode=${encodeURIComponent(referralCode)}`;
        console.log(`Inscription avec code de parrainage: ${referralCode}`);
      }

      const response = await fetch(signupUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      if (response.ok) {
        const result = await response.json();

        // Mettre à jour les données client
        this.customerData = result.customer;

        // Si un lien de parrainage a été généré, le stocker
        if (result.referralLink) {
          this.referralData = {
            url: result.referralLink,
            code: result.referralLink.split('ref=')[1]
          };
          this.updateReferralLink();
        }

        // Nettoyer le code de parrainage utilisé
        if (referralCode) {
          if (window.LoyaltyReferral && window.LoyaltyReferral.clear) {
            window.LoyaltyReferral.clear();
          } else {
            localStorage.removeItem('loyalty_referral_code');
            localStorage.removeItem('loyalty_referral_timestamp');
          }
          console.log('Code de parrainage utilisé et supprimé');
        }

        this.showMemberState();
        this.updatePointsPreview();

        // Afficher un message de succès
        this.showSuccessMessage(result.message);
      } else {
        throw new Error('Erreur lors de l\'inscription');
      }
    } catch (error) {
      console.error('Erreur lors de l\'inscription:', error);
      this.showErrorMessage('Impossible de vous inscrire au programme. Veuillez réessayer.');
    }
  }

  showSuccessMessage(message) {
    // Créer une notification temporaire
    const notification = document.createElement('div');
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: #4CAF50;
      color: white;
      padding: 16px 20px;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      z-index: 1000000;
      font-family: inherit;
      font-size: 14px;
      max-width: 300px;
    `;
    notification.textContent = message;

    document.body.appendChild(notification);

    // Supprimer après 5 secondes
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 5000);
  }

  showErrorMessage(message) {
    // Créer une notification d'erreur temporaire
    const notification = document.createElement('div');
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: #f44336;
      color: white;
      padding: 16px 20px;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      z-index: 1000000;
      font-family: inherit;
      font-size: 14px;
      max-width: 300px;
    `;
    notification.textContent = message;

    document.body.appendChild(notification);

    // Supprimer après 5 secondes
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 5000);
  }

  reload() {
    this.loadCustomerData();
  }

  handleResize() {
    if (!this.options.showOnMobile && window.innerWidth <= 768) {
      this.container.style.display = 'none';
    } else {
      this.container.style.display = '';
    }
  }

  hasSeenWidget() {
    return localStorage.getItem('loyalty-widget-seen') === 'true';
  }

  markWidgetSeen() {
    localStorage.setItem('loyalty-widget-seen', 'true');
  }

  // API publique
  updateCustomer(customerId) {
    this.options.customerId = customerId;
    this.loadCustomerData();
  }

  updatePoints(points) {
    if (this.customerData) {
      this.customerData.points = points;
      this.updatePointsPreview();
      this.updateProgress();
    }
  }
}

// Initialisation automatique
document.addEventListener('DOMContentLoaded', function() {
  // Vérifier si le widget existe
  if (document.getElementById('loyalty-widget')) {
    // Récupérer les options depuis la configuration Liquid
    const config = window.loyaltyWidgetConfig || {};

    const options = {
      shop: config.shop || '',
      customerId: config.customerId || null,
      position: config.position || 'bottom-right',
      primaryColor: config.primaryColor || '#2E7D32',
      secondaryColor: config.secondaryColor || '#4CAF50',
      showOnMobile: config.showOnMobile !== false,
      autoOpen: config.autoOpen || false
    };

    // Créer l'instance globale
    window.loyaltyWidget = new LoyaltyWidget(options);
  }
});
