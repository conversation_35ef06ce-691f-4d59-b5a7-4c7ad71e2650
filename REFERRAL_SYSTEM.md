# Système de Parrainage - Documentation

## Vue d'ensemble

Le système de parrainage permet aux clients de recommander votre boutique à leurs amis et de gagner des récompenses lorsque leurs filleuls effectuent des achats.

## Fonctionnalités

### 🔗 Génération de liens de parrainage
- Tokens uniques sécurisés en base64 (32 bytes)
- URLs au format : `https://votre-boutique.com?ref=eyxxxxxxxxxxxxxxxx`
- Redirection directe vers l'accueil de la boutique
- Gestion automatique de l'expiration des liens
- Prévention des doublons

### 🎯 Capture automatique des codes
- Détection du paramètre `?ref=TOKEN` dans l'URL
- Stockage persistant en localStorage
- Nettoyage automatique de l'URL après capture
- Ajout automatique aux attributs de commande
- Masquage automatique si programme de parrainage inactif

### 🏆 Attribution des récompenses
- **Filleul** : Reçoit ses points immédiatement lors de l'inscription
- **Parrain** : Reçoit ses points quand le filleul atteint le montant minimum d'achat
- Support des points, réductions ou montants fixes
- Vérification du montant minimum d'achat pour validation
- Protection contre l'auto-parrainage

### 📊 Suivi et statistiques
- Historique complet des parrainages
- Statuts : pending, completed, expired
- Statistiques globales et par client
- Interface d'administration dédiée avec gestion des clients
- Génération automatique de liens lors de l'inscription
- Section de test intégrée dans l'interface admin

## Configuration

### 1. Paramètres de parrainage

Accédez à **Programme > Parrainage** dans l'administration pour configurer :

- **État du programme** : Activer/désactiver le parrainage
- **Récompense du parrain** : Points ou réduction pour celui qui parraine
- **Récompense du filleul** : Points ou réduction pour le nouveau client
- **Montant minimum** : Montant minimum d'achat pour valider le parrainage
- **Durée d'expiration** : Nombre de jours avant expiration des liens
- **Message personnalisé** : Message affiché sur la page de parrainage

### 2. Types de récompenses

```json
{
  "type": "points",     // "points", "discount", "fixed"
  "amount": 100         // Nombre de points ou montant en euros
}
```

## Utilisation côté client

### 1. Génération d'un lien

Le widget de fidélité génère automatiquement un lien de parrainage unique pour chaque client membre. Le lien est affiché dans la section "Refer your friends".

### 2. Partage sur les réseaux sociaux

Le widget propose des boutons de partage pour :
- **Facebook** : Partage avec citation personnalisée
- **Twitter/X** : Tweet avec lien et message
- **Email** : Email pré-rempli avec invitation

### 3. Suivi des parrainages

Les clients peuvent voir leurs parrainages dans l'onglet "Referrals" de leur historique d'activité.

## Flux technique

### ⚠️ **LOGIQUE IMPORTANTE**
Le code de parrainage est utilisé **UNIQUEMENT lors de l'inscription** au programme de fidélité, **PAS pendant le checkout**. Voici le flux correct :

### 1. Génération du lien

```javascript
// API call pour générer un lien
const response = await fetch(`/api/proxy?prepath=generate-referral&shop=${shop}&customerId=${customerId}`);
const data = await response.json();
// Retourne : { success: true, referralUrl: "https://...", code: "TOKEN" }
```

### 2. Capture du code

```javascript
// Capture automatique lors du chargement de la page
const urlParams = new URLSearchParams(window.location.search);
const referralCode = urlParams.get('ref');
if (referralCode) {
  localStorage.setItem('loyalty_referral_code', referralCode);
  localStorage.setItem('loyalty_referral_timestamp', Date.now().toString());
  // Nettoyage de l'URL pour éviter que le code reste visible
  window.history.replaceState({}, document.title, window.location.pathname);
}
```

### 3. Inscription avec parrainage

```javascript
// Envoi du code lors de l'inscription au programme de fidélité
const referralCode = localStorage.getItem('loyalty_referral_code');
const signupUrl = `/api/proxy?prepath=signup&shop=${shop}&logged_in_customer_id=${customerId}&referralCode=${referralCode}`;

// RÉSULTAT :
// - Le filleul reçoit ses points immédiatement
// - Le parrainage est créé avec statut "pending"
// - Le code de parrainage est supprimé du localStorage
```

### 4. Validation lors des achats

Le webhook `ORDERS_CREATE` traite automatiquement :
1. Vérification s'il y a un parrainage en attente pour ce client
2. Validation du montant minimum d'achat
3. Attribution des récompenses au parrain si conditions remplies
4. Mise à jour du statut du parrainage (pending → completed)

**Note importante :** Le parrain ne reçoit ses points que quand le filleul effectue un achat qui atteint le montant minimum configuré.

## APIs disponibles

### Génération de lien
```
GET /api/proxy?prepath=generate-referral&shop={shop}&customerId={customerId}
```

### Récupération des parrainages
```
GET /api/proxy?prepath=customer-referrals&shop={shop}&customerId={customerId}
```

### Statistiques (admin)
```javascript
import { getReferralStats } from "app/models/Referral.server";
const stats = await getReferralStats(shop);
```

## Base de données

### Table Referral
```sql
- id: UUID
- shop: String
- referrerId: String (ID du parrain)
- referredId: String (ID du filleul, nullable)
- code: String (unique, token base64 sécurisé)
- status: String (pending/completed/expired)
- createdAt: DateTime
- completedAt: DateTime (nullable)
- expiresAt: DateTime
```

### Table ReferralSettings
```sql
- id: UUID
- shop: String (unique)
- active: Boolean
- referrerReward: String (JSON)
- referredReward: String (JSON)
- minimumPurchase: Float
- expiryDays: Integer
- customMessage: String
```

## Test du système

1. Accédez à **Programme > Parrainage** dans l'admin
2. Faites défiler jusqu'à la section "Gestion des liens de parrainage"
3. Générez un lien pour un client test en cliquant sur "Générer lien"
4. Copiez le lien généré et ouvrez-le dans un nouvel onglet
5. Vérifiez que le code est capturé dans la console du navigateur
6. Effectuez une commande test pour valider le parrainage

## Dépannage

### Le lien ne fonctionne pas
- Vérifiez que le programme de parrainage est actif
- Contrôlez que le client existe dans la base de données
- Vérifiez les logs pour les erreurs de génération

### Le code n'est pas capturé
- Assurez-vous que le widget est chargé sur la page
- Vérifiez la console pour les erreurs JavaScript
- Contrôlez que localStorage est disponible

### Les récompenses ne sont pas attribuées
- Vérifiez que le webhook `ORDERS_CREATE` est configuré
- Contrôlez que le montant minimum est atteint
- Vérifiez les logs du webhook pour les erreurs

## ❌ Ce qui N'EST PAS implémenté (volontairement)

### Codes de parrainage au checkout
- **Pas d'ajout automatique au panier** : Le code n'est pas ajouté aux attributs de commande
- **Pas d'interception des boutons d'achat** : Aucune logique de checkout n'est nécessaire
- **Pas de codes de réduction** : Les codes de parrainage ne sont pas des coupons de réduction

### Pourquoi cette approche ?
- **Simplicité** : Le parrainage fonctionne uniquement à l'inscription
- **Clarté** : Séparation nette entre inscription et validation
- **Performance** : Pas d'interception inutile des événements de checkout

## Sécurité

- Les codes de parrainage expirent automatiquement
- Protection contre l'auto-parrainage
- Validation des montants minimum
- Codes uniques pour éviter les conflits
- Vérification de l'appartenance des codes aux bons clients

## Performance

- Génération de codes optimisée avec vérification d'unicité
- Cache des paramètres de parrainage
- Requêtes indexées sur les codes et shops
- Nettoyage automatique des codes expirés (à implémenter)
