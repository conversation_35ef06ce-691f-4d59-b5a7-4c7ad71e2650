import { GraphqlQueryError, shopifyApi } from "@shopify/shopify-api";
import { ApiVersion } from "@shopify/shopify-app-remix/server";
import prisma from "../db.server";

/**
 * Récupérer le client GraphQL Admin pour une boutique
 * en utilisant le token d'accès stocké en base de données
 */
export async function getShopifyAdminClient(shop: string) {
  try {
    // Récupérer la session active pour cette boutique
    const session = await prisma.session.findFirst({
      where: {
        shop: shop,
        isOnline: false // Session offline pour les opérations admin
      },
      orderBy: {
        expires: 'desc' // Prendre la session la plus récente
      }
    });

    if (!session || !session.accessToken) {
      console.error(`No valid session found for shop: ${shop}`);
      return null;
    }

    // Vérifier si la session n'est pas expirée
    if (session.expires && session.expires < new Date()) {
      console.error(`Session expired for shop: ${shop}`);
      return null;
    }

    // Créer le client Shopify API
    const shopifyApiInstance = shopifyApi({
      apiKey: process.env.SHOPIFY_API_KEY!,
      apiSecretKey: process.env.SHOPIFY_API_SECRET!,
      scopes: process.env.SCOPES?.split(",") || [],
      hostName: process.env.SHOPIFY_APP_URL?.replace(/https?:\/\//, '') || '',
      apiVersion: ApiVersion.January25,
      isEmbeddedApp: true,
    });

    // Créer le client GraphQL avec le token d'accès
    const client = new shopifyApiInstance.clients.Graphql({
      session: {
        shop: session.shop,
        accessToken: session.accessToken
      }
    });

    return client;

  } catch (error) {
    console.error("Error getting Shopify admin client:", error);
    return null;
  }
}

/**
 * Exécuter une requête GraphQL avec gestion d'erreur
 */
export async function executeGraphQLQuery(
  shop: string,
  query: string,
  variables?: any
): Promise<{ success: boolean; data?: any; error?: string }> {
  try {
    const client = await getShopifyAdminClient(shop);

    if (!client) {
      return {
        success: false,
        error: "Impossible de récupérer le client Shopify pour cette boutique"
      };
    }

    const response = await client.request(query, {
      variables: variables
    });

    if (response.errors) {
      console.error("GraphQL errors:", response.errors);
      return {
        success: false,
        error: `Erreur GraphQL: ${response.errors[0]?.message || 'Erreur inconnue'}`
      };
    }

    return {
      success: true,
      data: response.data
    };

  } catch (error) {
    console.error("Error executing GraphQL query:", error);

    if (error instanceof GraphqlQueryError) {
      return {
        success: false,
        error: `Erreur GraphQL: ${error.message}`
      };
    }

    return {
      success: false,
      error: "Erreur lors de l'exécution de la requête GraphQL"
    };
  }
}

/**
 * Créer un code de réduction Shopify en utilisant le token stocké
 */
export async function createDiscountWithStoredToken(
  shop: string,
  discountData: {
    title: string;
    code: string;
    value: number;
    type: "fixed_amount" | "percentage";
    expiresAt?: Date;
    usageLimit?: number;
    minimumAmount?: number;
    customerId?: string; // ID Shopify du client
  }
): Promise<{ success: boolean; code?: string; discountId?: string; error?: string }> {
  try {
    let mutation: string;
    let variables: any;

    const discountInput = {
      title: discountData.title,
      code: discountData.code,
      startsAt: new Date().toISOString(),
      endsAt: discountData.expiresAt?.toISOString(),
      usageLimit: discountData.usageLimit || 1,
      appliesOncePerCustomer: true,
      customerSelection: discountData.customerId ? {
        customers: {
          add: [`gid://shopify/Customer/${discountData.customerId}`]
        }
      } : {
        all: true
      }
    };

    if (discountData.type === "fixed_amount") {
      // Code de réduction à montant fixe
      mutation = `
        mutation discountCodeBasicCreate($basicCodeDiscount: DiscountCodeBasicInput!) {
          discountCodeBasicCreate(basicCodeDiscount: $basicCodeDiscount) {
            codeDiscountNode {
              id
              codeDiscount {
                ... on DiscountCodeBasic {
                  title
                  codes(first: 1) {
                    nodes {
                      code
                    }
                  }
                  status
                  summary
                }
              }
            }
            userErrors {
              field
              message
            }
          }
        }
      `;

      variables = {
        basicCodeDiscount: {
          ...discountInput,
          customerGets: {
            value: {
              discountAmount: {
                amount: discountData.value.toString(),
                appliesOnEachItem: false
              }
            },
            items: {
              all: true
            }
          },
          minimumRequirement: discountData.minimumAmount ? {
            subtotal: {
              greaterThanOrEqualToSubtotal: discountData.minimumAmount.toString()
            }
          } : undefined
        }
      };
    } else {
      // Code de réduction en pourcentage
      mutation = `
        mutation discountCodeBasicCreate($basicCodeDiscount: DiscountCodeBasicInput!) {
          discountCodeBasicCreate(basicCodeDiscount: $basicCodeDiscount) {
            codeDiscountNode {
              id
              codeDiscount {
                ... on DiscountCodeBasic {
                  title
                  codes(first: 1) {
                    nodes {
                      code
                    }
                  }
                  status
                  summary
                }
              }
            }
            userErrors {
              field
              message
            }
          }
        }
      `;

      variables = {
        basicCodeDiscount: {
          ...discountInput,
          customerGets: {
            value: {
              percentage: discountData.value / 100 // Convertir en décimal
            },
            items: {
              all: true
            }
          },
          minimumRequirement: discountData.minimumAmount ? {
            subtotal: {
              greaterThanOrEqualToSubtotal: discountData.minimumAmount.toString()
            }
          } : undefined
        }
      };
    }

    const result = await executeGraphQLQuery(shop, mutation, variables);

    if (!result.success) {
      return {
        success: false,
        error: result.error
      };
    }

    const discountResult = result.data.discountCodeBasicCreate;

    if (discountResult.userErrors && discountResult.userErrors.length > 0) {
      return {
        success: false,
        error: discountResult.userErrors[0].message
      };
    }

    const createdCode = discountResult.codeDiscountNode?.codeDiscount?.codes?.nodes?.[0]?.code;
    const discountId = discountResult.codeDiscountNode?.id;

    if (!createdCode) {
      return {
        success: false,
        error: "Code de réduction non créé"
      };
    }

    return {
      success: true,
      code: createdCode,
      discountId: discountId
    };

  } catch (error) {
    console.error("Error creating discount with stored token:", error);
    return {
      success: false,
      error: "Erreur lors de la création du code de réduction"
    };
  }
}

/**
 * Créer un code de livraison gratuite Shopify
 */
export async function createFreeShippingWithStoredToken(
  shop: string,
  discountData: {
    title: string;
    code: string;
    expiresAt?: Date;
    usageLimit?: number;
    minimumAmount?: number;
    customerId?: string; // ID Shopify du client
  }
): Promise<{ success: boolean; code?: string; discountId?: string; error?: string }> {
  try {
    const mutation = `
      mutation discountCodeFreeShippingCreate($freeShippingCodeDiscount: DiscountCodeFreeShippingInput!) {
        discountCodeFreeShippingCreate(freeShippingCodeDiscount: $freeShippingCodeDiscount) {
          codeDiscountNode {
            id
            codeDiscount {
              ... on DiscountCodeFreeShipping {
                title
                codes(first: 1) {
                  nodes {
                    code
                  }
                }
                status
                summary
              }
            }
          }
          userErrors {
            field
            message
          }
        }
      }
    `;

    const variables = {
      freeShippingCodeDiscount: {
        title: discountData.title,
        code: discountData.code,
        startsAt: new Date().toISOString(),
        endsAt: discountData.expiresAt?.toISOString(),
        usageLimit: discountData.usageLimit || 1,
        appliesOncePerCustomer: true,
        customerSelection: discountData.customerId ? {
          customers: {
            add: [`gid://shopify/Customer/${discountData.customerId}`]
          }
        } : {
          all: true
        },
        minimumRequirement: discountData.minimumAmount ? {
          subtotal: {
            greaterThanOrEqualToSubtotal: discountData.minimumAmount.toString()
          }
        } : undefined
      }
    };

    const result = await executeGraphQLQuery(shop, mutation, variables);

    if (!result.success) {
      return {
        success: false,
        error: result.error
      };
    }

    const discountResult = result.data.discountCodeFreeShippingCreate;

    if (discountResult.userErrors && discountResult.userErrors.length > 0) {
      return {
        success: false,
        error: discountResult.userErrors[0].message
      };
    }

    const createdCode = discountResult.codeDiscountNode?.codeDiscount?.codes?.nodes?.[0]?.code;
    const discountId = discountResult.codeDiscountNode?.id;

    if (!createdCode) {
      return {
        success: false,
        error: "Code de livraison gratuite non créé"
      };
    }

    return {
      success: true,
      code: createdCode,
      discountId: discountId
    };

  } catch (error) {
    console.error("Error creating free shipping with stored token:", error);
    return {
      success: false,
      error: "Erreur lors de la création du code de livraison gratuite"
    };
  }
}
