import { Link, useLocation } from "@remix-run/react";
import { Card, BlockStack, Text, InlineStack } from "@shopify/polaris";
import { 
  HomeIcon, 
  SettingsIcon, 
  StarIcon, 
  GiftCardIcon, 
  MarketsIcon, 
  OrderIcon 
} from "@shopify/polaris-icons";

export function CustomNavigation() {
  const location = useLocation();

  const navigationItems = [
    {
      label: "Tableau de bord",
      icon: HomeIcon,
      url: "/app/admin",
      selected: location.pathname === "/app/admin",
    },
    {
      label: "Programme",
      icon: StarIcon,
      url: "/app/program",
      selected: location.pathname.startsWith("/app/program"),
      subItems: [
        {
          label: "Vue d'ensemble",
          url: "/app/program",
          selected: location.pathname === "/app/program",
        },
        {
          label: "Configuration des points",
          url: "/app/program/points",
          selected: location.pathname === "/app/program/points",
        },
        {
          label: "Parrainage",
          url: "/app/program/referrals",
          selected: location.pathname === "/app/program/referrals",
        },
        {
          label: "Programme VIP",
          url: "/app/program/vip",
          selected: location.pathname === "/app/program/vip",
        },
        {
          label: "Campagnes bonus",
          url: "/app/program/campaigns",
          selected: location.pathname === "/app/program/campaigns",
        },
      ],
    },
    {
      label: "Promotions",
      icon: MarketsIcon,
      url: "/app/promotions",
      selected: location.pathname === "/app/promotions",
    },
    {
      label: "Historique",
      icon: OrderIcon,
      url: "/app/history",
      selected: location.pathname === "/app/history",
    },
    {
      label: "Boutique de points",
      icon: GiftCardIcon,
      url: "/app/points-shop",
      selected: location.pathname === "/app/points-shop",
    },
  ];

  const settingsItems = [
    {
      label: "Paramètres généraux",
      url: "/app/settings",
      selected: location.pathname === "/app/settings",
    },
    {
      label: "Personnaliser le widget",
      url: "/app/settings/widget",
      selected: location.pathname === "/app/settings/widget",
    },
    {
      label: "Produits échangeables",
      url: "/app/settings/products",
      selected: location.pathname === "/app/settings/products",
    },
  ];

  const NavItem = ({ item, isSubItem = false }: { item: any, isSubItem?: boolean }) => (
    <Link
      to={item.url}
      style={{
        textDecoration: 'none',
        color: 'inherit',
        display: 'block',
        padding: isSubItem ? '8px 16px 8px 32px' : '12px 16px',
        borderRadius: '6px',
        backgroundColor: item.selected ? '#f0f8ff' : 'transparent',
        borderLeft: item.selected ? '3px solid #0066cc' : '3px solid transparent',
        marginBottom: '4px',
        transition: 'all 0.2s ease'
      }}
    >
      <InlineStack gap="200" align="start">
        {!isSubItem && item.icon && (
          <div style={{ width: '20px', height: '20px' }}>
            <item.icon />
          </div>
        )}
        <Text 
          as="span" 
          variant={isSubItem ? "bodySm" : "bodyMd"}
          fontWeight={item.selected ? "semibold" : "regular"}
          tone={item.selected ? "base" : "subdued"}
        >
          {item.label}
        </Text>
      </InlineStack>
    </Link>
  );

  return (
    <div style={{ width: '280px', height: '100vh', overflowY: 'auto', borderRight: '1px solid #e1e3e5', backgroundColor: '#fafbfb' }}>
      <div style={{ padding: '16px' }}>
        <Text as="h2" variant="headingMd" alignment="center">
          Loyalty App
        </Text>
      </div>
      
      <div style={{ padding: '0 16px' }}>
        <BlockStack gap="300">
          {/* Navigation principale */}
          <div>
            {navigationItems.map((item) => (
              <div key={item.url}>
                <NavItem item={item} />
                {item.subItems && item.selected && (
                  <div style={{ marginLeft: '8px' }}>
                    {item.subItems.map((subItem) => (
                      <NavItem key={subItem.url} item={subItem} isSubItem />
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>

          {/* Séparateur */}
          <div style={{ height: '1px', backgroundColor: '#e1e3e5', margin: '16px 0' }} />

          {/* Navigation des paramètres */}
          <div>
            <div style={{ marginBottom: '12px' }}>
              <InlineStack gap="200" align="start">
                <div style={{ width: '20px', height: '20px' }}>
                  <SettingsIcon />
                </div>
                <Text as="span" variant="bodyMd" fontWeight="semibold">
                  Paramètres
                </Text>
              </InlineStack>
            </div>
            {settingsItems.map((item) => (
              <NavItem key={item.url} item={item} isSubItem />
            ))}
          </div>
        </BlockStack>
      </div>
    </div>
  );
}
