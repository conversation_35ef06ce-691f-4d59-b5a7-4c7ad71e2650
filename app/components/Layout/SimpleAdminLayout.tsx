import { Frame, Page, TopBar } from "@shopify/polaris";
import { useState, useCallback } from "react";
import { CustomNavigation } from "./CustomNavigation";

interface SimpleAdminLayoutProps {
  children: React.ReactNode;
  title?: string;
}

export function SimpleAdminLayout({ children, title = "Administration" }: SimpleAdminLayoutProps) {
  const [userMenuActive, setUserMenuActive] = useState(false);

  const toggleUserMenuActive = useCallback(
    () => setUserMenuActive((userMenuActive) => !userMenuActive),
    [],
  );

  const userMenuActions = [
    {
      items: [{ content: "Déconnexion", onAction: () => console.log("logout") }],
    },
  ];

  return (
    <div style={{ display: 'flex', height: '100vh' }}>
      <CustomNavigation />
      <div style={{ flex: 1, overflow: 'hidden' }}>
        <Frame
          topBar={
            <TopBar
              showNavigationToggle={false}
              userMenu={
                <TopBar.UserMenu
                  actions={userMenuActions}
                  name="Admin"
                  initials="A"
                  open={userMenuActive}
                  onToggle={toggleUserMenuActive}
                />
              }
            />
          }
        >
          <Page title={title}>
            {children}
          </Page>
        </Frame>
      </div>
    </div>
  );
}
