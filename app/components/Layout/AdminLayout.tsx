import {
  Frame,
  Navigation,
  Page,
  TopBar,
  Text,
  InlineStack,
} from "@shopify/polaris";
import {
  HomeIcon,
  SettingsIcon,
  CursorIcon,
  GiftCardIcon,
  MarketsIcon,
  OrderIcon,
  EmailIcon,
  StarIcon,
} from "@shopify/polaris-icons";
import { useNavigate, useLocation, Link } from "@remix-run/react";
import { useState, useCallback, useEffect } from "react";
import { useTranslation } from "../../hooks/useTranslation";
import { LanguageSelector } from "../LanguageSelector/LanguageSelector";

interface AdminLayoutProps {
  children: React.ReactNode;
  title?: string;
}

export function AdminLayout({ children, title = "Administration" }: AdminLayoutProps) {
  const navigate = useNavigate();
  const location = useLocation();
  const [userMenuActive, setUserMenuActive] = useState(false);
  const [searchActive, setSearchActive] = useState(false);
  const [searchValue, setSearchValue] = useState("");
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const { t } = useTranslation();

  const toggleUserMenuActive = useCallback(
    () => setUserMenuActive((userMenuActive) => !userMenuActive),
    [],
  );

  // Fonction de recherche avec debounce
  useEffect(() => {
    const timeoutId = setTimeout(async () => {
      if (searchValue.trim()) {
        setIsSearching(true);
        try {
          const response = await fetch(`/api/search/customers?q=${encodeURIComponent(searchValue)}`);
          const results = await response.json();
          setSearchResults(results);
          setSearchActive(true);
        } catch (error) {
          console.error("Search error:", error);
          setSearchResults([]);
        } finally {
          setIsSearching(false);
        }
      } else {
        setSearchResults([]);
        setSearchActive(false);
      }
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [searchValue]);

  const handleSearchChange = useCallback((value: string) => {
    setSearchValue(value);
  }, []);

  const handleSearchResultClick = useCallback((url: string) => {
    navigate(url);
    setSearchActive(false);
    setSearchValue("");
    setSearchResults([]);
  }, [navigate]);

  const toggleSearchActive = useCallback(
    () => setSearchActive((searchActive) => !searchActive),
    [],
  );

  const userMenuActions = [
    {
      items: [{ content: t("common.logout"), onAction: () => console.log("logout") }],
    },
  ];

  const navigationItems = [
    {
      label: t("admin.navigation.dashboard"),
      icon: HomeIcon,
      url: "/app/admin",
      selected: location.pathname === "/app/admin",
    },
    {
      label: t("admin.navigation.program"),
      icon: StarIcon,
      url: "/app/program",
      selected: location.pathname.startsWith("/app/program"),
      subNavigationItems: [
        {
          label: t("admin.navigation.overview"),
          url: "/app/program",
          selected: location.pathname === "/app/program",
        },
        {
          label: t("admin.navigation.pointsConfig"),
          url: "/app/program/points",
          selected: location.pathname === "/app/program/points",
        },
        {
          label: t("admin.navigation.referrals"),
          url: "/app/program/referrals",
          selected: location.pathname === "/app/program/referrals",
        },
        {
          label: t("admin.navigation.vipProgram"),
          url: "/app/program/vip",
          selected: location.pathname === "/app/program/vip",
        },
        {
          label: t("admin.navigation.bonusCampaigns"),
          url: "/app/program/campaigns",
          selected: location.pathname === "/app/program/campaigns",
        },
      ],
    },
    {
      label: t("admin.navigation.promotions"),
      icon: MarketsIcon,
      url: "/app/promotions",
      selected: location.pathname === "/app/promotions",
    },
    {
      label: t("admin.navigation.history"),
      icon: OrderIcon,
      url: "/app/history",
      selected: location.pathname === "/app/history",
    },
    {
      label: t("admin.navigation.pointsShop"),
      icon: GiftCardIcon,
      url: "/app/points-shop",
      selected: location.pathname === "/app/points-shop",
    },
  ];

  const secondaryNavItems = [
    {
      label: t("admin.navigation.settings"),
      icon: SettingsIcon,
      url: "/app/settings",
      selected: location.pathname.startsWith("/app/settings"),
      subNavigationItems: [
        {
          label: t("admin.navigation.generalSettings"),
          url: "/app/settings",
          selected: location.pathname === "/app/settings",
        },
        {
          label: t("admin.navigation.customizeWidget"),
          url: "/app/settings/widget",
          selected: location.pathname === "/app/settings/widget",
        },
        {
          label: t("admin.navigation.exchangeableProducts"),
          url: "/app/settings/products",
          selected: location.pathname === "/app/settings/products",
        },
      ],
    },
  ];

  const searchField = (
    <TopBar.SearchField
      onChange={handleSearchChange}
      value={searchValue}
      placeholder={t("common.search")}
    />
  );

  return (
    <Frame
      navigation={
        <Navigation location={location.pathname}>
          <Navigation.Section
            items={navigationItems}
          />
          <Navigation.Section
            items={secondaryNavItems}
            separator
          />
        </Navigation>
      }
      topBar={
        <TopBar
          showNavigationToggle
          userMenu={
            <TopBar.UserMenu
              actions={userMenuActions}
              name="Admin"
              initials="A"
              open={userMenuActive}
              onToggle={toggleUserMenuActive}
            />
          }
          searchField={searchField}
          searchResultsVisible={searchActive}
          onSearchResultsDismiss={() => setSearchActive(false)}
          searchResults={
            searchResults.length > 0 ? (
              <div style={{ padding: "16px", maxHeight: "300px", overflowY: "auto" }}>
                {searchResults.map((customer) => (
                  <div
                    key={customer.id}
                    onClick={() => handleSearchResultClick(customer.url)}
                    style={{
                      padding: "8px 12px",
                      cursor: "pointer",
                      borderRadius: "4px",
                      marginBottom: "4px",
                      display: "flex",
                      justifyContent: "space-between",
                      alignItems: "center",
                      backgroundColor: "transparent",
                      transition: "background-color 0.2s"
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.backgroundColor = "#f6f6f7";
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.backgroundColor = "transparent";
                    }}
                  >
                    <div>
                      <div style={{ fontWeight: "500", fontSize: "14px" }}>
                        {customer.name || customer.email}
                      </div>
                      <div style={{ fontSize: "12px", color: "#6d7175" }}>
                        {customer.email} • {customer.points} points
                      </div>
                    </div>
                    <div style={{
                      fontSize: "11px",
                      padding: "2px 6px",
                      borderRadius: "3px",
                      backgroundColor: customer.type === "member" ? "#d4edda" : "#e2e3e5",
                      color: customer.type === "member" ? "#155724" : "#495057"
                    }}>
                      {customer.type === "member" ? t("admin.customers.member") : t("admin.customers.guest")}
                    </div>
                  </div>
                ))}
                {isSearching && (
                  <div style={{ textAlign: "center", padding: "8px", color: "#6d7175" }}>
                    {t("common.loading")}
                  </div>
                )}
              </div>
            ) : searchValue.trim() && !isSearching ? (
              <div style={{ padding: "16px", textAlign: "center", color: "#6d7175" }}>
                {t("common.noResults")}
              </div>
            ) : undefined
          }
          secondaryMenu={
            <InlineStack gap="200" align="end">
              <LanguageSelector />
            </InlineStack>
          }
        />
      }
    >
      {/* narrowWidth || fullWidth */}
      <Page title={title} >
        {children}
      </Page>
    </Frame>
  );
}
