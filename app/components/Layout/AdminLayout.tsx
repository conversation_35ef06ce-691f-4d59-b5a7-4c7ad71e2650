import {
  Frame,
  Navigation,
  Page,
  TopBar,
  Text,
  InlineStack,
} from "@shopify/polaris";
import {
  HomeIcon,
  SettingsIcon,
  CursorIcon,
  GiftCardIcon,
  MarketsIcon,
  OrderIcon,
  EmailIcon,
  StarIcon,
} from "@shopify/polaris-icons";
import { useNavigate, useLocation, Link } from "@remix-run/react";
import { useState, useCallback } from "react";
import { useTranslation } from "../../hooks/useTranslation";
import { LanguageSelector } from "../LanguageSelector/LanguageSelector";

interface AdminLayoutProps {
  children: React.ReactNode;
  title?: string;
}

export function AdminLayout({ children, title = "Administration" }: AdminLayoutProps) {
  const navigate = useNavigate();
  const location = useLocation();
  const [userMenuActive, setUserMenuActive] = useState(false);
  const [searchActive, setSearchActive] = useState(false);
  const { t } = useTranslation();

  const toggleUserMenuActive = useCallback(
    () => setUserMenuActive((userMenuActive) => !userMenuActive),
    [],
  );

  const toggleSearchActive = useCallback(
    () => setSearchActive((searchActive) => !searchActive),
    [],
  );

  const userMenuActions = [
    {
      items: [{ content: t("common.logout"), onAction: () => console.log("logout") }],
    },
  ];

  const navigationItems = [
    {
      label: t("admin.navigation.dashboard"),
      icon: HomeIcon,
      url: "/app/admin",
      selected: location.pathname === "/app/admin",
    },
    {
      label: t("admin.navigation.program"),
      icon: StarIcon,
      url: "/app/program",
      selected: location.pathname.startsWith("/app/program"),
      subNavigationItems: [
        {
          label: t("admin.navigation.overview"),
          url: "/app/program",
          selected: location.pathname === "/app/program",
        },
        {
          label: t("admin.navigation.pointsConfig"),
          url: "/app/program/points",
          selected: location.pathname === "/app/program/points",
        },
        {
          label: t("admin.navigation.referrals"),
          url: "/app/program/referrals",
          selected: location.pathname === "/app/program/referrals",
        },
        {
          label: t("admin.navigation.vipProgram"),
          url: "/app/program/vip",
          selected: location.pathname === "/app/program/vip",
        },
        {
          label: t("admin.navigation.bonusCampaigns"),
          url: "/app/program/campaigns",
          selected: location.pathname === "/app/program/campaigns",
        },
      ],
    },
    {
      label: t("admin.navigation.promotions"),
      icon: MarketsIcon,
      url: "/app/promotions",
      selected: location.pathname === "/app/promotions",
    },
    {
      label: t("admin.navigation.history"),
      icon: OrderIcon,
      url: "/app/history",
      selected: location.pathname === "/app/history",
    },
    {
      label: t("admin.navigation.pointsShop"),
      icon: GiftCardIcon,
      url: "/app/points-shop",
      selected: location.pathname === "/app/points-shop",
    },
  ];

  const secondaryNavItems = [
    {
      label: t("admin.navigation.settings"),
      icon: SettingsIcon,
      url: "/app/settings",
      selected: location.pathname.startsWith("/app/settings"),
      subNavigationItems: [
        {
          label: t("admin.navigation.generalSettings"),
          url: "/app/settings",
          selected: location.pathname === "/app/settings",
        },
        {
          label: t("admin.navigation.customizeWidget"),
          url: "/app/settings/widget",
          selected: location.pathname === "/app/settings/widget",
        },
        {
          label: t("admin.navigation.exchangeableProducts"),
          url: "/app/settings/products",
          selected: location.pathname === "/app/settings/products",
        },
      ],
    },
  ];

  const searchField = (
    <TopBar.SearchField
      onChange={() => {}}
      value=""
      placeholder={t("common.search")}
    />
  );

  return (
    <Frame
      navigation={
        <Navigation location={location.pathname}>
          <Navigation.Section
            items={navigationItems}
          />
          <Navigation.Section
            items={secondaryNavItems}
            separator
          />
        </Navigation>
      }
      topBar={
        <TopBar
          showNavigationToggle
          userMenu={
            <TopBar.UserMenu
              actions={userMenuActions}
              name="Admin"
              initials="A"
              open={userMenuActive}
              onToggle={toggleUserMenuActive}
            />
          }
          searchField={searchField}
          searchResultsVisible={searchActive}
          onSearchResultsDismiss={() => setSearchActive(false)}
          secondaryMenu={
            <InlineStack gap="200" align="end">
              <LanguageSelector />
            </InlineStack>
          }
        />
      }
    >
      {/* narrowWidth || fullWidth */}
      <Page title={title} >
        {children}
      </Page>
    </Frame>
  );
}
