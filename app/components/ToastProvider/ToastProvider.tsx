import { createContext, useContext, useState, useCallback, ReactNode } from 'react';
import { Toast } from '@shopify/polaris';

interface ToastContextType {
  showToast: (content: string, duration?: number) => void;
}

const ToastContext = createContext<ToastContextType | undefined>(undefined);

export function useToast() {
  const context = useContext(ToastContext);
  if (!context) {
    throw new Error('useToast must be used within a ToastProvider');
  }
  return context;
}

interface ToastProviderProps {
  children: ReactNode;
}

export function ToastProvider({ children }: ToastProviderProps) {
  const [toastActive, setToastActive] = useState(false);
  const [toastContent, setToastContent] = useState('');

  const showToast = useCallback((content: string, duration = 3000) => {
    setToastContent(content);
    setToastActive(true);
    
    // Auto-dismiss after duration
    setTimeout(() => {
      setToastActive(false);
    }, duration);
  }, []);

  const toastMarkup = toastActive ? (
    <Toast
      content={toastContent}
      onDismiss={() => setToastActive(false)}
    />
  ) : null;

  return (
    <ToastContext.Provider value={{ showToast }}>
      {children}
      {toastMarkup}
    </ToastContext.Provider>
  );
}
