import { useState, useCallback, useEffect } from "react";
import {
  Modal,
  FormLayout,
  TextField,
  Button,
  Select,
  RadioButton,
  Checkbox,
  BlockStack,
  Text,
  ButtonGroup
} from "@shopify/polaris";

interface WayToEarnModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (data: WayToEarnFormData) => void;
  wayToEarn?: any;
  isLoading?: boolean;
}

export interface WayToEarnFormData {
  name: string;
  description: string;
  actionType: string;
  earningType: "increments" | "fixed";
  earningValue: string;
  icon: string;
  isActive: boolean;
}

export function WayToEarnModal({ isOpen, onClose, onSave, wayToEarn, isLoading = false }: WayToEarnModalProps) {
  const [formState, setFormState] = useState<WayToEarnFormData>({
    name: wayToEarn?.name || "",
    description: wayToEarn?.description || "",
    actionType: wayToEarn?.actionType || "order",
    earningType: wayToEarn?.earningType || "increments",
    earningValue: wayToEarn?.earningValue?.toString() || "",
    icon: wayToEarn?.icon || "order",
    isActive: wayToEarn?.isActive ?? true
  });

  const [errors, setErrors] = useState<Partial<WayToEarnFormData>>({});

  // Mettre à jour le formulaire quand wayToEarn change
  useEffect(() => {
    if (wayToEarn) {
      setFormState({
        name: wayToEarn.name || "",
        description: wayToEarn.description || "",
        actionType: wayToEarn.actionType || "order",
        earningType: wayToEarn.earningType || "increments",
        earningValue: wayToEarn.earningValue?.toString() || "",
        icon: wayToEarn.icon || "order",
        isActive: wayToEarn.isActive ?? true
      });
    } else {
      setFormState({
        name: "",
        description: "",
        actionType: "order",
        earningType: "increments",
        earningValue: "",
        icon: "order",
        isActive: true
      });
    }
    setErrors({});
  }, [wayToEarn, isOpen]);

  const handleSubmit = useCallback(() => {
    // Validation
    const newErrors: Partial<WayToEarnFormData> = {};

    if (!formState.name.trim()) {
      newErrors.name = "Le nom est requis";
    }

    if (!formState.description.trim()) {
      newErrors.description = "La description est requise";
    }

    if (!formState.earningValue || parseFloat(formState.earningValue) <= 0) {
      newErrors.earningValue = "La valeur doit être positive";
    }

    setErrors(newErrors);

    if (Object.keys(newErrors).length === 0) {
      onSave(formState);
    }
  }, [formState, onSave]);

  const handleClose = useCallback(() => {
    setFormState({
      name: "",
      description: "",
      actionType: "order",
      earningType: "increments",
      earningValue: "",
      icon: "order",
      isActive: true
    });
    setErrors({});
    onClose();
  }, [onClose]);

  const actionTypeOptions = [
    { label: "Passer une commande", value: "order" },
    { label: "Inscription au programme", value: "signup" },
    { label: "Anniversaire", value: "birthday" },
    { label: "Parrainage", value: "referral" },
    { label: "Laisser un avis", value: "review" },
    { label: "Partage sur les réseaux", value: "social_share" }
  ];

  const iconOptions = [
    { label: "Commande", value: "order" },
    { label: "Achat", value: "purchase" },
    { label: "Inscription", value: "signup" },
    { label: "Anniversaire", value: "birthday" },
    { label: "Parrainage", value: "referral" },
    { label: "Avis", value: "review" }
  ];

  const getEarningValueLabel = () => {
    return formState.earningType === "increments"
      ? "Points gagnés par €1 dépensé"
      : "Nombre de points fixes";
  };

  const getEarningValueHelpText = () => {
    return formState.earningType === "increments"
      ? "Nombre de points que le client gagne pour chaque euro dépensé (ex: 5 = 5 points par €1)"
      : "Nombre exact de points accordés pour cette action (ex: 100 points pour l'inscription)";
  };

  return (
    <Modal
      open={isOpen}
      onClose={handleClose}
      title={wayToEarn ? "Modifier la façon de gagner des points" : "Ajouter une façon de gagner des points"}
      primaryAction={{
        content: wayToEarn ? "Enregistrer" : "Créer",
        onAction: handleSubmit,
        loading: isLoading
      }}
      secondaryActions={[
        {
          content: "Annuler",
          onAction: handleClose
        }
      ]}
    >
      <Modal.Section>
        <FormLayout>
          <TextField
            label="Nom"
            value={formState.name}
            onChange={(value) => setFormState(prev => ({ ...prev, name: value }))}
            placeholder="ex: Passer une commande"
            error={errors.name}
            autoComplete="off"
            requiredIndicator
          />

          <TextField
            label="Description"
            value={formState.description}
            onChange={(value) => setFormState(prev => ({ ...prev, description: value }))}
            placeholder="ex: Gagnez des points à chaque achat"
            error={errors.description}
            autoComplete="off"
            requiredIndicator
          />

          <Select
            label="Type d'action"
            options={actionTypeOptions}
            value={formState.actionType}
            onChange={(value) => setFormState(prev => ({ ...prev, actionType: value }))}
            helpText="Sélectionnez quand cette action doit être déclenchée"
          />

          <BlockStack gap="200">
            <Text as="h3" variant="headingXs">Type de gain</Text>
            <RadioButton
              label="Points par euro dépensé (ex: 5 points pour chaque €1)"
              checked={formState.earningType === "increments"}
              onChange={() => setFormState(prev => ({ ...prev, earningType: "increments" }))}
            />
            <RadioButton
              label="Points fixes pour l'action (ex: 100 points pour l'inscription)"
              checked={formState.earningType === "fixed"}
              onChange={() => setFormState(prev => ({ ...prev, earningType: "fixed" }))}
            />
          </BlockStack>

          <TextField
            label={getEarningValueLabel()}
            value={formState.earningValue}
            onChange={(value) => setFormState(prev => ({ ...prev, earningValue: value }))}
            type="number"
            step={formState.earningType === "increments" ? 0.1 : 1}
            min="0"
            error={errors.earningValue}
            helpText={getEarningValueHelpText()}
            autoComplete="off"
            requiredIndicator
          />

          <Select
            label="Icône"
            options={iconOptions}
            value={formState.icon}
            onChange={(value) => setFormState(prev => ({ ...prev, icon: value }))}
          />

          <Checkbox
            label="Actif"
            checked={formState.isActive}
            onChange={(checked) => setFormState(prev => ({ ...prev, isActive: checked }))}
          />
        </FormLayout>
      </Modal.Section>
    </Modal>
  );
}
