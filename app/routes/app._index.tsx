import { useLoaderData } from "@remix-run/react";

import type { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/node";
import { useFetcher } from "@remix-run/react";
import {
  Page,
  Layout,
  Card,
  Text,
  Grid,
  BlockStack,
  DataTable
} from "@shopify/polaris";
import { AdminLayout } from "../components/Layout/AdminLayout";
import { useTranslation } from "../hooks/useTranslation";
import db from "../db.server";
import { authenticate } from "../shopify.server";
import { json, redirect } from "@remix-run/node";
import { lazy, Suspense, useState, useEffect } from "react";



const LineChart = lazy(() => import("@shopify/polaris-viz").then(module => ({ default: module.LineChart })));
const BarChart = lazy(() => import("@shopify/polaris-viz").then(module => ({ default: module.BarChart })));

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const { session } = await authenticate.admin(request);
  const shop = session.shop;

  // Récupérer les points totaux et le nombre de membres actifs
  const pointsStats = await db.customer.aggregate({
    where: {
      shop: shop,
    },
    _sum: {
      points: true,
    },
    _count: {
      customerId: true,
    },
  });

  // Calculer le taux d'utilisation (points utilisés / points gagnés) sur les 30 derniers jours
  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

  // Récupérer les données pour le graphique d'évolution sur 30 jours
  const dailyPoints = await db.pointsHistory.groupBy({
    by: ['timestamp'],
    where: {
      customer: {
        shop: shop,
      },
      timestamp: {
        gte: thirtyDaysAgo,
      },
    },
    _sum: {
      points: true,
    },
    orderBy: {
      timestamp: 'asc',
    },
  });

  // Récupérer la distribution des points par type d'action
  const pointsByType = await db.pointsHistory.groupBy({
    by: ['action'],
    where: {
      customer: {
        shop: shop,
      },
      timestamp: {
        gte: thirtyDaysAgo,
      },
    },
    _sum: {
      points: true,
    },
  });

  const pointsActivity = await db.pointsHistory.groupBy({
    by: ['action'],
    where: {
      customer: {
        shop: shop,
      },
      timestamp: {
        gte: thirtyDaysAgo,
      },
    },
    _sum: {
      points: true,
    },
  });

  // Calculer le taux d'utilisation
  const pointsEarned = pointsActivity.find(p => p.action === 'earn')?._sum.points || 0;
  const pointsRedeemed = Math.abs(pointsActivity.find(p => p.action === 'redeem')?._sum.points || 0);
  const redemptionRate = pointsEarned > 0 ? Math.round((pointsRedeemed / pointsEarned) * 100) : 0;

  // Récupérer l'activité récente
  const recentActivity = await db.pointsHistory.findMany({
    where: {
      customer: {
        shop: shop,
      },
    },
    select: {
      customer: {
        select: {
          customerId: true,
          firstName: true,
          lastName: true,
          email: true,
        },
      },
      action: true,
      points: true,
      timestamp: true,
    },
    orderBy: {
      timestamp: 'desc',
    },
    take: 10,
  });

  // Formater l'activité récente pour le DataTable
  const formattedActivity = recentActivity.map(activity => {
    const customerName = activity.customer.firstName && activity.customer.lastName
      ? `${activity.customer.firstName} ${activity.customer.lastName}`
      : activity.customer.email || `Client ${activity.customer.customerId}`;

    return [
      customerName,
      activity.action,
      `${activity.points > 0 ? '+' : ''}${activity.points} points`,
      new Date(activity.timestamp).toLocaleDateString('fr-FR'),
    ];
  });

  // Formater les données pour les graphiques
  const pointsEvolutionData = {
    data: dailyPoints.map(day => ({
      key: new Date(day.timestamp).toLocaleDateString('fr-FR'),
      value: day._sum.points || 0,
    })),
  };

  const pointsDistributionData = {
    data: pointsByType.map(type => ({
      key: type.action,
      value: Math.abs(type._sum.points || 0),
    })),
  };

  return json({
    stats: {
      totalPoints: pointsStats._sum.points || 0,
      activeMembers: pointsStats._count.customerId || 0,
      redemptionRate: `${redemptionRate}%`,
      recentActivity: formattedActivity,
      pointsEvolution: pointsEvolutionData,
      pointsDistribution: pointsDistributionData,
    }
  });
};




export default function Admin() {
  const { stats } = useLoaderData<typeof loader>();
  const [isClient, setIsClient] = useState(false);
  const { t } = useTranslation();

  useEffect(() => {
    setIsClient(true); // Indique que nous sommes côté client après l'hydratation
  }, []);

  return (
    <AdminLayout title={t("admin.dashboard.title")}>
      <Layout>
        <Layout.Section>
          <Grid>
            <Grid.Cell columnSpan={{ xs: 6, sm: 3, md: 3, lg: 3 }}>
              <Card>
                <BlockStack gap="200">
                  <Text as="h3" variant="headingMd">{t("admin.dashboard.totalPoints")}</Text>
                  <Text as="p" variant="bodyLg" fontWeight="bold">
                    {stats.totalPoints.toLocaleString()}
                  </Text>
                </BlockStack>
              </Card>
            </Grid.Cell>
            <Grid.Cell columnSpan={{ xs: 6, sm: 3, md: 3, lg: 3 }}>
              <Card>
                <BlockStack gap="200">
                  <Text as="h3" variant="headingMd">{t("admin.dashboard.activeMembers")}</Text>
                  <Text as="p" variant="bodyLg" fontWeight="bold">
                    {stats.activeMembers}
                  </Text>
                </BlockStack>
              </Card>
            </Grid.Cell>
            <Grid.Cell columnSpan={{ xs: 6, sm: 3, md: 3, lg: 3 }}>
              <Card>
                <BlockStack gap="200">
                  <Text as="h3" variant="headingMd">{t("admin.dashboard.redemptionRate")}</Text>
                  <Text as="p" variant="bodyLg" fontWeight="bold">
                    {stats.redemptionRate}
                  </Text>
                </BlockStack>
              </Card>
            </Grid.Cell>
          </Grid>
        </Layout.Section>

        <Layout.Section>
          <Card>
            <BlockStack gap="400">
              <Text as="h3" variant="headingMd">{t("admin.dashboard.pointsEvolution")}</Text>
              {isClient ? (
                <Suspense fallback={<div>{t("admin.dashboard.loadingChart")}</div>}>
                  <LineChart data={[stats.pointsEvolution]} theme="Light" />
                </Suspense>
              ) : (
                <div>{t("admin.dashboard.loadingChart")}</div>
              )}
            </BlockStack>
          </Card>
        </Layout.Section>

        <Layout.Section>
          <Card>
            <BlockStack gap="400">
              <Text as="h3" variant="headingMd">{t("admin.dashboard.pointsDistribution")}</Text>
              {isClient ? (
                <Suspense fallback={<div>{t("admin.dashboard.loadingChart")}</div>}>
                  <BarChart data={[stats.pointsDistribution]} theme="Light" />
                </Suspense>
              ) : (
                <div>{t("admin.dashboard.loadingChart")}</div>
              )}
            </BlockStack>
          </Card>
        </Layout.Section>

        <Layout.Section>
          <Card>
            <BlockStack gap="400">
              <Text as="h3" variant="headingMd">{t("admin.dashboard.recentActivity")}</Text>
              <DataTable
                columnContentTypes={["text", "text", "text", "text"]}
                headings={[
                  t("admin.dashboard.tableHeaders.customer"),
                  t("admin.dashboard.tableHeaders.action"),
                  t("admin.dashboard.tableHeaders.points"),
                  t("admin.dashboard.tableHeaders.date")
                ]}
                rows={stats.recentActivity}
              />
            </BlockStack>
          </Card>
        </Layout.Section>
      </Layout>
    </AdminLayout>
  );
}
