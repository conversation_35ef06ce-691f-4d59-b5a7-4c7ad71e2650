import { json, type ActionFunctionArgs, type LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData, useSubmit, useNavigate } from "@remix-run/react";
import { authenticate } from "../shopify.server";
import { getSiteSettings, upsertSiteSettings, getDefaultSiteSettings, type SiteSettingsData } from "../models/SiteSettings.server";
import {
  Card,
  Layout,
  Page,
  BlockStack,
  InlineStack,
  Text,
  TextField,
  Select,
  Checkbox,
  Button,
  Grid,
  Box,
  Divider,
  Toast,
  Frame,
} from "@shopify/polaris";
import { AdminLayout } from "../components/Layout/AdminLayout";
import { useState, useCallback } from "react";

import { WidgetSimulator } from "../components/WidgetSimulatorNew2";
import { ColorPicker } from "../components/ColorPicker";

interface LoaderData {
  settings: SiteSettingsData | null;
  customerData: {
    name: string;
    email: string;
    points: number;
    orders: number;
    initials: string;
  };
}

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const { admin, session } = await authenticate.admin(request);
  const { shop } = session;

  try {
    const settings = await getSiteSettings(shop);

    // Données client par défaut pour la simulation
    const customerData = {
      name: "Marie Dupont",
      email: "<EMAIL>",
      points: 1250,
      orders: 8,
      initials: "MD"
    };

    return json({ settings, customerData });
  } catch (error) {
    console.error("Error loading widget settings:", error);
    return json({ settings: getDefaultSiteSettings(shop) });
  }
};

export const action = async ({ request }: ActionFunctionArgs) => {
  const { session } = await authenticate.admin(request);
  const { shop } = session;

  try {
    const formData = await request.formData();
    const rawData = Object.fromEntries(formData);

    // Convertir les valeurs booléennes
    const settingsData: any = { ...rawData };
    const booleanFields = ['widgetShadow', 'widgetAnimation', 'showPointsOnButton', 'widgetEnabled'];
    booleanFields.forEach(field => {
      if (rawData[field] !== undefined) {
        settingsData[field] = rawData[field] === 'true';
      }
    });

    await upsertSiteSettings(shop, settingsData);

    return json({ success: true, message: "Paramètres du widget sauvegardés avec succès" });
  } catch (error) {
    console.error("Error saving widget settings:", error);
    return json({ error: "Erreur lors de la sauvegarde" }, { status: 500 });
  }
};

export default function WidgetSettings() {
  const { settings, customerData } = useLoaderData<typeof loader>() as LoaderData;
  const submit = useSubmit();
  const navigate = useNavigate();

  // États pour les paramètres du widget
  const [widgetColor, setWidgetColor] = useState(settings?.widgetColor || '#2E7D32');
  const [widgetSecondaryColor, setWidgetSecondaryColor] = useState(settings?.widgetSecondaryColor || '#4CAF50');
  const [widgetTextColor, setWidgetTextColor] = useState(settings?.widgetTextColor || '#FFFFFF');
  const [widgetPosition, setWidgetPosition] = useState(settings?.widgetPosition || 'bottom-right');
  const [widgetSize, setWidgetSize] = useState(settings?.widgetSize || 'medium');
  const [widgetBorderRadius, setWidgetBorderRadius] = useState(settings?.widgetBorderRadius || 'rounded');
  const [widgetShadow, setWidgetShadow] = useState(settings?.widgetShadow !== false);
  const [widgetAnimation, setWidgetAnimation] = useState(settings?.widgetAnimation !== false);
  const [showPointsOnButton, setShowPointsOnButton] = useState(settings?.showPointsOnButton !== false);
  const [pointsName, setPointsName] = useState(settings?.pointsName || 'Points');
  const [welcomeMessage, setWelcomeMessage] = useState(settings?.welcomeMessage || '');

  // État pour les toasts
  const [toastActive, setToastActive] = useState(false);
  const [toastMessage, setToastMessage] = useState('');
  const [toastError, setToastError] = useState(false);

  const handleSubmit = useCallback(() => {
    const formData = new FormData();
    formData.append('widgetColor', widgetColor);
    formData.append('widgetSecondaryColor', widgetSecondaryColor);
    formData.append('widgetTextColor', widgetTextColor);
    formData.append('widgetPosition', widgetPosition);
    formData.append('widgetSize', widgetSize);
    formData.append('widgetBorderRadius', widgetBorderRadius);
    formData.append('widgetShadow', widgetShadow.toString());
    formData.append('widgetAnimation', widgetAnimation.toString());
    formData.append('showPointsOnButton', showPointsOnButton.toString());
    formData.append('pointsName', pointsName);
    formData.append('welcomeMessage', welcomeMessage);

    submit(formData, { method: 'post' });

    setToastMessage('Paramètres du widget sauvegardés avec succès');
    setToastError(false);
    setToastActive(true);
  }, [
    widgetColor, widgetSecondaryColor, widgetTextColor, widgetPosition,
    widgetSize, widgetBorderRadius, widgetShadow, widgetAnimation,
    showPointsOnButton, pointsName, welcomeMessage, submit
  ]);

  const toggleToastActive = useCallback(() => setToastActive((active) => !active), []);

  const toastMarkup = toastActive ? (
    <Toast
      content={toastMessage}
      onDismiss={toggleToastActive}
      error={toastError}
    />
  ) : null;

  const positionOptions = [
    { label: 'Bas droite', value: 'bottom-right' },
    { label: 'Bas gauche', value: 'bottom-left' },
    { label: 'Haut droite', value: 'top-right' },
    { label: 'Haut gauche', value: 'top-left' },
  ];

  const sizeOptions = [
    { label: 'Petit', value: 'small' },
    { label: 'Moyen', value: 'medium' },
    { label: 'Grand', value: 'large' },
  ];

  const borderRadiusOptions = [
    { label: 'Carré', value: 'square' },
    { label: 'Arrondi', value: 'rounded' },
    { label: 'Pilule', value: 'pill' },
  ];

  return (
    <Frame>
      <AdminLayout title="Personnalisation du widget">
        <Page fullWidth>
          <Layout>
            <Layout.Section variant="oneThird">
              <BlockStack gap="500">
              <Card>
                <BlockStack gap="400">
                  <Text as="h2" variant="headingMd">
                    Couleurs du widget
                  </Text>

                  <ColorPicker
                    label="Couleur principale"
                    value={widgetColor}
                    onChange={setWidgetColor}
                  />

                  <ColorPicker
                    label="Couleur secondaire"
                    value={widgetSecondaryColor}
                    onChange={setWidgetSecondaryColor}
                  />

                  <ColorPicker
                    label="Couleur du texte"
                    value={widgetTextColor}
                    onChange={setWidgetTextColor}
                  />
                </BlockStack>
              </Card>

              <Card>
                <BlockStack gap="400">
                  <Text as="h2" variant="headingMd">
                    Apparence
                  </Text>

                  <Select
                    label="Position du widget"
                    options={positionOptions}
                    value={widgetPosition}
                    onChange={setWidgetPosition}
                  />

                  <Select
                    label="Taille du widget"
                    options={sizeOptions}
                    value={widgetSize}
                    onChange={setWidgetSize}
                  />

                  <Select
                    label="Bordures"
                    options={borderRadiusOptions}
                    value={widgetBorderRadius}
                    onChange={setWidgetBorderRadius}
                  />

                  <Checkbox
                    label="Ombre portée"
                    checked={widgetShadow}
                    onChange={setWidgetShadow}
                  />

                  <Checkbox
                    label="Animation"
                    checked={widgetAnimation}
                    onChange={setWidgetAnimation}
                  />

                  <Checkbox
                    label="Afficher les points sur le bouton"
                    checked={showPointsOnButton}
                    onChange={setShowPointsOnButton}
                  />
                </BlockStack>
              </Card>

              <Card>
                <BlockStack gap="400">
                  <Text as="h2" variant="headingMd">
                    Contenu
                  </Text>

                  <TextField
                    label="Nom des points"
                    value={pointsName}
                    onChange={setPointsName}
                    placeholder="Points"
                    autoComplete="off"
                  />

                  <TextField
                    label="Message de bienvenue"
                    value={welcomeMessage}
                    onChange={setWelcomeMessage}
                    multiline={3}
                    placeholder="Bienvenue dans notre programme de fidélité !"
                    autoComplete="off"
                  />
                </BlockStack>
              </Card>

                <Card>
                  <InlineStack align="end">
                    <Button variant="primary" onClick={handleSubmit}>
                      Sauvegarder les paramètres
                    </Button>
                  </InlineStack>
                </Card>
              </BlockStack>
            </Layout.Section>

            <Layout.Section >
              <Card>
                <BlockStack gap="400">
                  <Text as="h2" variant="headingMd">
                    🎯 Aperçu en temps réel
                  </Text>
                  <Text as="p" variant="bodySm" tone="subdued">
                    Les changements s'appliquent instantanément
                  </Text>

                  <WidgetSimulator
                    primaryColor={widgetColor}
                    secondaryColor={widgetSecondaryColor}
                    textColor={widgetTextColor}
                    position={widgetPosition}
                    programName={pointsName || 'Programme de Fidélité'}
                    programDescription="Aperçu en temps réel de votre widget"
                    welcomeMessage={welcomeMessage || 'Bienvenue dans notre programme de fidélité !'}
                    customerData={customerData}
                    widgetSize={widgetSize}
                    widgetBorderRadius={widgetBorderRadius}
                    widgetShadow={widgetShadow}
                    widgetAnimation={widgetAnimation}
                    showPointsOnButton={showPointsOnButton}
                  />
                </BlockStack>
              </Card>
            </Layout.Section>
          </Layout>
          {toastMarkup}
        </Page>
      </AdminLayout>
    </Frame>
  );
}
