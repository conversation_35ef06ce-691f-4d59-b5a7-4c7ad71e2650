import { json, type ActionFunctionArgs } from "@remix-run/node";
import { useLoaderData, useSubmit } from "@remix-run/react";
import {
  Layout,
  Card,
  ResourceList,
  ResourceItem,
  Text,
  Button,
  Modal,
  FormLayout,
  TextField,
  DatePicker,
  BlockStack,
} from "@shopify/polaris";
import { useState } from "react";
import { AdminLayout } from "../components/Layout/AdminLayout";

export const loader = async () => {
  // TODO: Implement real data fetching
  const mockPromotions = [
    {
      id: "1",
      title: "Double Points Weekend",
      multiplier: 2,
      startDate: "2024-03-22",
      endDate: "2024-03-24",
      status: "active",
    },
    {
      id: "2",
      title: "VIP Bonus Points",
      multiplier: 1.5,
      startDate: "2024-04-01",
      endDate: "2024-04-30",
      status: "scheduled",
    },
  ];

  return json({ promotions: mockPromotions });
};

export const action = async ({ request }: ActionFunctionArgs) => {
  const formData = await request.formData();
  const data = Object.fromEntries(formData);
  
  // TODO: Implement real data saving
  console.log("Promotion to save:", data);
  
  return json({ success: true });
};

export default function AdminPromotions() {
  const { promotions } = useLoaderData<typeof loader>();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const submit = useSubmit();

  const handleSubmit = (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    submit(event.currentTarget);
    setIsModalOpen(false);
  };

  return (
    <AdminLayout title="Promotions">
      <Layout>
        <Layout.Section>
          <Card>
            <BlockStack gap="400">
              <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}>
                <Text as="h2" variant="headingMd">Promotions actives et programmées</Text>
                <Button variant="primary" onClick={() => setIsModalOpen(true)}>
                  Nouvelle promotion
                </Button>
              </div>
              
              <ResourceList
                resourceName={{ singular: "promotion", plural: "promotions" }}
                items={promotions}
                renderItem={(item) => (
                  <ResourceItem id={item.id} onClick={() => {}}>
                    <BlockStack gap="200">
                      <Text as="h3" variant="headingSm">{item.title}</Text>
                      <Text as="p" variant="bodyMd">
                        Multiplicateur: x{item.multiplier}
                      </Text>
                      <Text as="p" variant="bodySm">
                        {item.startDate} - {item.endDate}
                      </Text>
                      <Text as="p" variant="bodySm">
                        Statut: {item.status === "active" ? "Actif" : "Programmé"}
                      </Text>
                    </BlockStack>
                  </ResourceItem>
                )}
              />
            </BlockStack>
          </Card>
        </Layout.Section>

        <Modal
          open={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          title="Nouvelle promotion"
        >
          <Modal.Section>
            <form onSubmit={handleSubmit}>
              <FormLayout>
                <TextField
                  label="Titre de la promotion"
                  type="text"
                  name="title"
                  autoComplete="off"
                />
                <TextField
                  label="Multiplicateur de points"
                  type="number"
                  name="multiplier"
                  step={0.1}
                  value="1.0"
                  autoComplete="off"
                />
                <TextField
                  label="Date de début"
                  type="date"
                  name="startDate"
                  autoComplete="off"
                />
                <TextField
                  label="Date de fin"
                  type="date"
                  name="endDate"
                  autoComplete="off"
                />
                <Button submit variant="primary">
                  Créer la promotion
                </Button>
              </FormLayout>
            </form>
          </Modal.Section>
        </Modal>
      </Layout>
    </AdminLayout>
  );
} 