import { json } from "@remix-run/node";
import {
  Card,
  Layout,
  BlockStack,
  Text,
  Grid,
} from "@shopify/polaris";
import { AdminLayout } from "../components/Layout/AdminLayout";
import { useTranslation } from "../hooks/useTranslation";

export const loader = async () => {
  // TODO: Implement real data fetching
  const mockStats = {
    totalPoints: 25000,
    activeCustomers: 150,
    redemptionsThisMonth: 45,
    averagePoints: 167,
  };

  return json({ stats: mockStats });
};

export default function AdminDashboard() {
  const { t } = useTranslation();

  return (
    <AdminLayout title={t("admin.dashboard.title")}>
      <Layout>
        <Layout.Section>
          <Grid>
            <Grid.Cell columnSpan={{ xs: 6, sm: 3, md: 3, lg: 3 }}>
              <Card>
                <BlockStack gap="200">
                  <Text as="h3" variant="headingSm">
                    {t("admin.dashboard.pointsInCirculation")}
                  </Text>
                  <Text as="p" variant="headingLg">
                    25 000
                  </Text>
                </BlockStack>
              </Card>
            </Grid.Cell>
            <Grid.Cell columnSpan={{ xs: 6, sm: 3, md: 3, lg: 3 }}>
              <Card>
                <BlockStack gap="200">
                  <Text as="h3" variant="headingSm">
                    {t("admin.dashboard.activeMembers")}
                  </Text>
                  <Text as="p" variant="headingLg">
                    150
                  </Text>
                </BlockStack>
              </Card>
            </Grid.Cell>
            <Grid.Cell columnSpan={{ xs: 6, sm: 3, md: 3, lg: 3 }}>
              <Card>
                <BlockStack gap="200">
                  <Text as="h3" variant="headingSm">
                    {t("admin.dashboard.rewardsThisMonth")}
                  </Text>
                  <Text as="p" variant="headingLg">
                    45
                  </Text>
                </BlockStack>
              </Card>
            </Grid.Cell>
            <Grid.Cell columnSpan={{ xs: 6, sm: 3, md: 3, lg: 3 }}>
              <Card>
                <BlockStack gap="200">
                  <Text as="h3" variant="headingSm">
                    {t("admin.dashboard.averagePointsPerCustomer")}
                  </Text>
                  <Text as="p" variant="headingLg">
                    167
                  </Text>
                </BlockStack>
              </Card>
            </Grid.Cell>
          </Grid>
        </Layout.Section>

        <Layout.Section>
          <Card>
            <BlockStack gap="400">
              <Text as="h2" variant="headingMd">
                {t("admin.dashboard.recentActivity")}
              </Text>
              <Text as="p" variant="bodyMd">
                {t("admin.dashboard.dataAvailableSoon")}
              </Text>
            </BlockStack>
          </Card>
        </Layout.Section>
      </Layout>
    </AdminLayout>
  );
}
