import { json, type ActionFunctionArgs } from "@remix-run/node";
import { useLoaderData, useSubmit } from "@remix-run/react";
import {
  Layout,
  Card,
  FormLayout,
  TextField,
  Button,
  BlockStack,
  Text,
  Banner,
  Box,
  InlineStack,
  Icon,
  DataTable,
  Badge,
  EmptyState,
  Select,
  DatePicker,
} from "@shopify/polaris";
import { CalendarIcon } from "@shopify/polaris-icons";
import { AdminLayout } from "app/components/Layout/AdminLayout";
import { useState } from "react";

export const loader = async () => {
  // TODO: Implémenter la récupération des données réelles
  const mockData = {
    campaigns: [
      {
        id: 1,
        name: "Double points weekend",
        type: "multiplier",
        value: "2",
        startDate: "2024-03-15",
        endDate: "2024-03-17",
        status: "scheduled",
        target: "all",
      },
      {
        id: 2,
        name: "Points bonus collection été",
        type: "fixed",
        value: "500",
        startDate: "2024-06-01",
        endDate: "2024-08-31",
        status: "draft",
        target: "collection",
      },
    ],
  };

  return json({ data: mockData });
};

export const action = async ({ request }: ActionFunctionArgs) => {
  const formData = await request.formData();
  const data = Object.fromEntries(formData);
  
  // TODO: Implémenter la sauvegarde réelle des données
  console.log("Data to save:", data);
  
  return json({ success: true });
};

export default function ProgramCampaigns() {
  const { data } = useLoaderData<typeof loader>();
  const submit = useSubmit();
  const [showNewCampaign, setShowNewCampaign] = useState(false);
  const [selectedDates, setSelectedDates] = useState({
    start: new Date(),
    end: new Date(),
  });

  const handleSubmit = (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    submit(event.currentTarget);
  };

  const campaignTypes = [
    { label: "Multiplicateur de points", value: "multiplier" },
    { label: "Points fixes", value: "fixed" },
    { label: "Pourcentage bonus", value: "percentage" },
  ];

  const targetTypes = [
    { label: "Tous les produits", value: "all" },
    { label: "Collection spécifique", value: "collection" },
    { label: "Produits spécifiques", value: "products" },
  ];

  const rows = data.campaigns.map((campaign) => [
    <Text as="span" variant="bodyMd" fontWeight="bold">{campaign.name}</Text>,
    <Text as="span" variant="bodyMd">
      {campaign.type === "multiplier" ? `x${campaign.value}` : 
       campaign.type === "fixed" ? `${campaign.value} points` :
       `+${campaign.value}%`}
    </Text>,
    <Text as="span" variant="bodyMd">{campaign.target}</Text>,
    <Text as="span" variant="bodyMd">
      {new Date(campaign.startDate).toLocaleDateString()} - {new Date(campaign.endDate).toLocaleDateString()}
    </Text>,
    <Badge tone={
      campaign.status === "active" ? "success" :
      campaign.status === "scheduled" ? "info" :
      "attention"
    }>
      {campaign.status === "active" ? "Active" :
       campaign.status === "scheduled" ? "Planifiée" :
       "Brouillon"}
    </Badge>,
    <Button size="slim">Modifier</Button>,
  ]);

  return (
    <AdminLayout title="Campagnes bonus">
      <Layout>
        <Layout.Section>
          <BlockStack gap="500">
            <Card>
              <BlockStack gap="400">
                <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}>
                  <Text as="h2" variant="headingMd">Campagnes en cours</Text>
                  <Button onClick={() => setShowNewCampaign(true)}>
                    Créer une campagne
                  </Button>
                </div>
                {data.campaigns.length === 0 ? (
                  <EmptyState
                    heading="Créez votre première campagne"
                    action={{
                      content: "Créer une campagne",
                      onAction: () => setShowNewCampaign(true),
                    }}
                    image=""
                  >
                    <p>Augmentez l'engagement de vos clients avec des points bonus temporaires.</p>
                  </EmptyState>
                ) : (
                  <DataTable
                    columnContentTypes={["text", "text", "text", "text", "text", "text"]}
                    headings={["Nom", "Récompense", "Cible", "Période", "Statut", "Actions"]}
                    rows={rows}
                  />
                )}
              </BlockStack>
            </Card>

            {showNewCampaign && (
              <Card>
                <BlockStack gap="400">
                  <Text as="h2" variant="headingMd">Nouvelle campagne</Text>
                  <form onSubmit={handleSubmit}>
                    <FormLayout>
                      <TextField
                        label="Nom de la campagne"
                        type="text"
                        name="name"
                        autoComplete="off"
                        required
                      />

                      <Select
                        label="Type de récompense"
                        options={campaignTypes}
                        name="type"
                        required
                      />

                      <TextField
                        label="Valeur"
                        type="number"
                        name="value"
                        autoComplete="off"
                        required
                        min={0}
                        helpText="Multiplicateur, nombre de points ou pourcentage selon le type"
                      />

                      <Select
                        label="Cible"
                        options={targetTypes}
                        name="target"
                        required
                        helpText="Sélectionnez les produits concernés par la campagne"
                      />

                      <Box>
                        <Text as="h3" variant="headingSm">Période de la campagne</Text>
                        <DatePicker
                          month={selectedDates.start.getMonth()}
                          year={selectedDates.start.getFullYear()}
                          selected={{
                            start: selectedDates.start,
                            end: selectedDates.end,
                          }}
                          onChange={({ start, end }) => {
                            setSelectedDates({ start, end });
                          }}
                          allowRange
                        />
                      </Box>

                      <InlineStack gap="300">
                        <Button submit variant="primary">
                          Créer la campagne
                        </Button>
                        <Button onClick={() => setShowNewCampaign(false)}>
                          Annuler
                        </Button>
                      </InlineStack>
                    </FormLayout>
                  </form>
                </BlockStack>
              </Card>
            )}
          </BlockStack>
        </Layout.Section>

        <Layout.Section secondary>
          <BlockStack gap="500">
            <Card>
              <BlockStack gap="400">
                <Text as="h2" variant="headingMd">Statistiques</Text>
                <BlockStack gap="200">
                  <Text as="p" variant="bodyMd">
                    Campagnes actives : <Text as="span" variant="headingSm">0</Text>
                  </Text>
                  <Text as="p" variant="bodyMd">
                    Campagnes planifiées : <Text as="span" variant="headingSm">1</Text>
                  </Text>
                  <Text as="p" variant="bodyMd">
                    Points bonus distribués : <Text as="span" variant="headingSm">0</Text>
                  </Text>
                </BlockStack>
              </BlockStack>
            </Card>

            <Card>
              <BlockStack gap="400">
                <Text as="h2" variant="headingMd">Aide</Text>
                <Text as="p" variant="bodyMd">
                  Les campagnes bonus permettent d'offrir temporairement plus de points aux clients sur certains produits.
                  Vous pouvez créer des multiplicateurs de points, des bonus fixes ou des pourcentages supplémentaires.
                </Text>
                <Text as="p" variant="bodyMd">
                  Planifiez vos campagnes à l'avance pour les périodes importantes comme les soldes ou les fêtes.
                </Text>
              </BlockStack>
            </Card>
          </BlockStack>
        </Layout.Section>
      </Layout>
    </AdminLayout>
  );
} 