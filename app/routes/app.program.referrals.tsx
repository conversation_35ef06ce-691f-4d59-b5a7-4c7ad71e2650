import { json, type ActionFunctionArgs, type LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData, useSubmit } from "@remix-run/react";
import { useState, useCallback } from "react";
import { authenticate } from "../shopify.server";
import { getReferralSettings, upsertReferralSettings, getDefaultReferralSettings } from "../models/ReferralSettings.server";
import { getProgramStats } from "../models/ProgramStats.server";
import { createReferralLink, getReferralStats, hasActiveReferralLink } from "../models/Referral.server";
import { getAllCustomers } from "../models/Customer.server";
import {
  Layout,
  Card,
  FormLayout,
  TextField,
  Button,
  BlockStack,
  Text,
  Banner,
  Box,
  InlineStack,
  Icon,
  ChoiceList,
  Select,
  Badge,
  List,
  Toast,
  Frame,
  DataTable,
  Divider,
} from "@shopify/polaris";
import { GiftCardIcon } from "@shopify/polaris-icons";
import { AdminLayout } from "app/components/Layout/AdminLayout";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const { session } = await authenticate.admin(request);
  const shop = session.shop;

  try {
    // Récupérer les paramètres de parrainage existants
    let settings = await getReferralSettings(shop);

    // Si aucun paramètre n'existe, utiliser les valeurs par défaut
    if (!settings) {
      settings = getDefaultReferralSettings();
    }

    // Récupérer les statistiques du programme et les clients
    const [stats, referralStats, customersData] = await Promise.all([
      getProgramStats(shop),
      getReferralStats(shop),
      getAllCustomers(shop, 1, 10) // Récupérer les 10 premiers clients
    ]);

    // Vérifier quels clients ont déjà des liens de parrainage
    const customersWithReferralStatus = await Promise.all(
      customersData.customers.map(async (customer) => ({
        ...customer,
        hasReferralLink: await hasActiveReferralLink(shop, customer.customerId)
      }))
    );

    return json({ settings, stats, referralStats, customers: customersWithReferralStatus });
  } catch (error) {
    console.error("Error loading referral settings:", error);
    // En cas d'erreur, retourner les paramètres par défaut
    return json({
      settings: getDefaultReferralSettings(),
      stats: {},
      referralStats: { total: 0, completed: 0, pending: 0, conversionRate: 0 },
      customers: []
    });
  }
};

export const action = async ({ request }: ActionFunctionArgs) => {
  const { session } = await authenticate.admin(request);
  const shop = session.shop;

  const formData = await request.formData();
  const actionType = formData.get("actionType") as string;

  try {
    if (actionType === "toggle") {
      // Basculer l'état actif/inactif
      const currentSettings = await getReferralSettings(shop) || getDefaultReferralSettings();
      const updatedSettings = {
        ...currentSettings,
        active: !currentSettings.active
      };

      await upsertReferralSettings(shop, updatedSettings);
      return json({ success: true, message: `Programme ${updatedSettings.active ? 'activé' : 'désactivé'}` });
    }

    if (actionType === "generateReferral") {
      // Générer un lien de parrainage pour un client
      const customerId = formData.get("customerId") as string;
      if (!customerId) {
        return json({ error: "Customer ID required" }, { status: 400 });
      }

      const result = await createReferralLink(shop, customerId);
      if (result.success) {
        return json({ success: true, referralUrl: result.referralUrl, message: "Lien de parrainage généré avec succès" });
      } else {
        return json({ error: result.error }, { status: 400 });
      }
    }

    if (actionType === "save") {
      // Sauvegarder tous les paramètres
      const settingsData = {
        active: formData.get("active") === "true",
        referrerReward: {
          type: formData.get("referrerRewardType") as "points" | "discount" | "fixed",
          amount: parseInt(formData.get("referrerRewardAmount") as string) || 0
        },
        referredReward: {
          type: formData.get("referredRewardType") as "points" | "discount" | "fixed",
          amount: parseInt(formData.get("referredRewardAmount") as string) || 0
        },
        minimumPurchase: parseInt(formData.get("minimumPurchase") as string) || 0,
        expiryDays: parseInt(formData.get("expiryDays") as string) || 30,
        customMessage: formData.get("customMessage") as string || ""
      };

      await upsertReferralSettings(shop, settingsData);
      return json({ success: true, message: "Paramètres sauvegardés avec succès" });
    }

    return json({ error: "Action non reconnue" }, { status: 400 });
  } catch (error) {
    console.error("Error saving referral settings:", error);
    return json({ error: "Erreur lors de la sauvegarde" }, { status: 500 });
  }
};

export default function ProgramReferrals() {
  const loaderData = useLoaderData<typeof loader>();
  const initialSettings = loaderData.settings;
  const stats = (loaderData as any).stats;
  const referralStats = (loaderData as any).referralStats;
  const customers = (loaderData as any).customers;
  const submit = useSubmit();

  // État local pour les formulaires
  const [settings, setSettings] = useState(initialSettings);
  const [hasChanges, setHasChanges] = useState(false);
  const [toastActive, setToastActive] = useState(false);
  const [toastMessage, setToastMessage] = useState("");

  const showToast = (message: string) => {
    setToastMessage(message);
    setToastActive(true);
  };

  const handleGenerateReferral = (customerId: string) => {
    const formData = new FormData();
    formData.append("actionType", "generateReferral");
    formData.append("customerId", customerId);

    submit(formData, {
      method: "post",
      preventScrollReset: true
    });
  };

  const handleSubmit = (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    const formData = new FormData(event.currentTarget);
    formData.append("actionType", "save");
    formData.append("active", settings.active.toString());
    formData.append("referrerRewardType", settings.referrerReward.type);
    formData.append("referrerRewardAmount", settings.referrerReward.amount.toString());
    formData.append("referredRewardType", settings.referredReward.type);
    formData.append("referredRewardAmount", settings.referredReward.amount.toString());
    formData.append("minimumPurchase", settings.minimumPurchase.toString());
    formData.append("expiryDays", settings.expiryDays.toString());
    formData.append("customMessage", settings.customMessage);

    submit(formData, {
      method: "post",
      preventScrollReset: true
    });

    setHasChanges(false);
    showToast("Paramètres sauvegardés avec succès !");
  };

  const handleToggleActive = () => {
    const formData = new FormData();
    formData.append("actionType", "toggle");

    submit(formData, {
      method: "post",
      preventScrollReset: true
    });

    // Mettre à jour l'état local immédiatement
    setSettings(prev => ({ ...prev, active: !prev.active }));
    showToast(settings.active ? "Programme désactivé" : "Programme activé");
  };

  // Handlers pour mettre à jour l'état
  const updateSetting = useCallback((field: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [field]: value
    }));
    setHasChanges(true);
  }, []);

  const updateNestedSetting = useCallback((parent: keyof typeof settings, field: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [parent]: {
        ...(prev[parent] as any),
        [field]: value
      }
    }));
    setHasChanges(true);
  }, [settings]);

  const rewardTypes = [
    { label: "Points", value: "points" },
    { label: "Réduction fixe (€)", value: "fixed" },
    { label: "Pourcentage (%)", value: "discount" },
  ];

  // Fonctions pour les textes dynamiques
  const getRewardLabel = (type: string) => {
    switch (type) {
      case "points": return "Montant en points";
      case "fixed": return "Montant de la réduction (€)";
      case "discount": return "Pourcentage de réduction (%)";
      default: return "Montant de la récompense";
    }
  };

  const getRewardText = (amount: number, type: string) => {
    switch (type) {
      case "points": return `${amount} points`;
      case "fixed": return `${amount}€ de réduction`;
      case "discount": return `${amount}% de réduction`;
      default: return `${amount}`;
    }
  };

  const toastMarkup = toastActive ? (
    <Toast
      content={toastMessage}
      onDismiss={() => setToastActive(false)}
      duration={4000}
    />
  ) : null;

  return (
    <Frame>
      <AdminLayout title="Programme de parrainage">
      <Layout>
        <Layout.Section>
          <BlockStack gap="500">
            {/* Description du programme de parrainage */}
            <Card>
              <BlockStack gap="400">
                <Text as="h2" variant="headingMd">Programme de parrainage</Text>
                <Text as="p" variant="bodyMd" tone="subdued">
                  Le programme de parrainage permet à vos clients fidèles de recommander votre boutique à leurs amis et famille.
                  Quand un client parraine un ami qui effectue son premier achat, les deux parties reçoivent des récompenses.
                  C'est un excellent moyen d'acquérir de nouveaux clients tout en récompensant la fidélité de vos clients existants.
                </Text>
                <Text as="p" variant="bodyMd" tone="subdued">
                  <strong>Comment ça fonctionne :</strong>
                </Text>
                <List type="number">
                  <List.Item>Le parrain partage son code de parrainage unique avec ses amis</List.Item>
                  <List.Item>Le filleul utilise ce code lors de sa première commande</List.Item>
                  <List.Item>Si la commande atteint le montant minimum, les récompenses sont distribuées</List.Item>
                  <List.Item>Le parrain et le filleul reçoivent leurs récompenses respectives</List.Item>
                </List>
              </BlockStack>
            </Card>

            <Card>
              <BlockStack gap="400">
                <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}>
                  <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
                    <Text as="h2" variant="headingMd">État du programme</Text>
                    <Badge tone={settings.active ? "success" : "attention"}>
                      {settings.active ? "Actif" : "Inactif"}
                    </Badge>
                  </div>
                  <Button
                    tone={settings.active ? "critical" : "success"}
                    onClick={handleToggleActive}
                  >
                    {settings.active ? "Désactiver" : "Activer"}
                  </Button>
                </div>
                <Text as="p" variant="bodyMd" tone="subdued">
                  {settings.active
                    ? "Vos clients peuvent actuellement parrainer leurs amis et gagner des récompenses."
                    : "Le programme de parrainage est actuellement désactivé. Activez-le pour permettre à vos clients de parrainer leurs amis."
                  }
                </Text>
              </BlockStack>
            </Card>

            <Card>
              <BlockStack gap="400">
                <Text as="h2" variant="headingMd">Récompense du parrain</Text>
                <Box paddingBlockEnd="400">
                  <InlineStack gap="200" align="center">
                    <Icon source={GiftCardIcon} />
                    <Text as="span" variant="bodyMd">
                      Le parrain reçoit {getRewardText(settings.referrerReward.amount, settings.referrerReward.type)}
                    </Text>
                  </InlineStack>
                </Box>
                <form onSubmit={handleSubmit}>
                  <FormLayout>
                    <Select
                      label="Type de récompense"
                      options={rewardTypes}
                      name="referrerRewardType"
                      value={settings.referrerReward.type}
                      onChange={(value) => updateNestedSetting('referrerReward', 'type', value)}
                    />
                    <TextField
                      label={getRewardLabel(settings.referrerReward.type)}
                      type="number"
                      name="referrerRewardAmount"
                      value={settings.referrerReward.amount.toString()}
                      onChange={(value) => updateNestedSetting('referrerReward', 'amount', parseInt(value) || 0)}
                      autoComplete="off"
                      min={0}
                      max={settings.referrerReward.type === "discount" ? 100 : undefined}
                    />
                  </FormLayout>
                </form>
              </BlockStack>
            </Card>

            <Card>
              <BlockStack gap="400">
                <Text as="h2" variant="headingMd">Récompense du filleul</Text>
                <Box paddingBlockEnd="400">
                  <InlineStack gap="200" align="center">
                    <Icon source={GiftCardIcon} />
                    <Text as="span" variant="bodyMd">
                      Le filleul reçoit {getRewardText(settings.referredReward.amount, settings.referredReward.type)}
                    </Text>
                  </InlineStack>
                </Box>
                <form onSubmit={handleSubmit}>
                  <FormLayout>
                    <Select
                      label="Type de récompense"
                      options={rewardTypes}
                      name="referredRewardType"
                      value={settings.referredReward.type}
                      onChange={(value) => updateNestedSetting('referredReward', 'type', value)}
                    />
                    <TextField
                      label={getRewardLabel(settings.referredReward.type)}
                      type="number"
                      name="referredRewardAmount"
                      value={settings.referredReward.amount.toString()}
                      onChange={(value) => updateNestedSetting('referredReward', 'amount', parseInt(value) || 0)}
                      autoComplete="off"
                      min={0}
                      max={settings.referredReward.type === "discount" ? 100 : undefined}
                    />
                  </FormLayout>
                </form>
              </BlockStack>
            </Card>

            <Card>
              <BlockStack gap="400">
                <Text as="h2" variant="headingMd">Conditions</Text>
                <form onSubmit={handleSubmit}>
                  <FormLayout>
                    <TextField
                      label="Montant minimum d'achat pour le filleul (€)"
                      type="number"
                      name="minimumPurchase"
                      value={settings.minimumPurchase.toString()}
                      onChange={(value) => updateSetting('minimumPurchase', parseInt(value) || 0)}
                      autoComplete="off"
                      helpText="Montant minimum que le filleul doit dépenser pour valider le parrainage"
                      min={0}
                    />
                    <TextField
                      label="Durée de validité de l'invitation (jours)"
                      type="number"
                      name="expiryDays"
                      value={settings.expiryDays.toString()}
                      onChange={(value) => updateSetting('expiryDays', parseInt(value) || 0)}
                      autoComplete="off"
                      helpText="Nombre de jours pendant lesquels l'invitation reste valide"
                      min={0}
                    />
                  </FormLayout>
                </form>
              </BlockStack>
            </Card>

            <Card>
              <BlockStack gap="400">
                <Text as="h2" variant="headingMd">Personnalisation</Text>
                <form onSubmit={handleSubmit}>
                  <FormLayout>
                    <TextField
                      label="Message d'invitation"
                      name="customMessage"
                      value={settings.customMessage}
                      onChange={(value) => updateSetting('customMessage', value)}
                      autoComplete="off"
                      multiline={3}
                      helpText="Ce message sera affiché sur la page de parrainage"
                    />
                    <Button submit variant="primary">Enregistrer les modifications</Button>
                  </FormLayout>
                </form>
              </BlockStack>
            </Card>

            {/* Section de test et gestion des clients */}
            <Divider />

            <Card>
              <BlockStack gap="400">
                <Text as="h2" variant="headingMd">Gestion des liens de parrainage</Text>
                <Text as="p" variant="bodyMd" tone="subdued">
                  Générez et gérez les liens de parrainage pour vos clients.
                </Text>

                {customers.length > 0 ? (
                  <DataTable
                    columnContentTypes={['text', 'text', 'text', 'numeric', 'text', 'text']}
                    headings={['Nom', 'Email', 'Type', 'Points', 'Statut Parrainage', 'Action']}
                    rows={customers.map(customer => [
                      `${customer.firstName || ''} ${customer.lastName || ''}`.trim() || 'N/A',
                      customer.email || 'N/A',
                      customer.type === 'member' ? <Badge tone="success">Membre</Badge> : <Badge tone="info">Invité</Badge>,
                      customer.points.toString(),
                      customer.hasReferralLink ? <Badge tone="success">Lien actif</Badge> : <Badge tone="attention">Pas de lien</Badge>,
                      <Button
                        size="slim"
                        onClick={() => handleGenerateReferral(customer.customerId)}
                        disabled={customer.hasReferralLink}
                      >
                        {customer.hasReferralLink ? 'Lien existant' : 'Générer lien'}
                      </Button>
                    ])}
                  />
                ) : (
                  <Text as="p" variant="bodyMd" tone="subdued">
                    Aucun client trouvé. Assurez-vous d'avoir des clients dans votre boutique.
                  </Text>
                )}
              </BlockStack>
            </Card>

            <Card>
              <BlockStack gap="400">
                <Text as="h2" variant="headingMd">Format des liens de parrainage</Text>
                <Text as="p" variant="bodyMd">
                  Les liens générés suivent ce format :
                </Text>
                <Text as="p" variant="bodyMd" fontWeight="bold">
                  https://votre-boutique.myshopify.com?ref=eyxxxxxxxxxxxxxxxx
                </Text>
                <Text as="p" variant="bodyMd" tone="subdued">
                  Où "eyxxxxxxxxxxxxxxxx" est un token unique sécurisé en base64.
                </Text>
                <Text as="p" variant="bodyMd" tone="subdued">
                  <strong>Comment ça fonctionne :</strong>
                </Text>
                <List type="number">
                  <List.Item>Le client génère son lien via le widget de fidélité</List.Item>
                  <List.Item>Il partage le lien sur les réseaux sociaux ou par email</List.Item>
                  <List.Item>Un ami clique sur le lien et est redirigé vers votre boutique</List.Item>
                  <List.Item>Le code est capturé automatiquement et stocké</List.Item>
                  <List.Item>Lors de l'achat, le parrainage est validé et les récompenses distribuées</List.Item>
                </List>
              </BlockStack>
            </Card>
          </BlockStack>
        </Layout.Section>

        <Layout.Section variant="oneThird">
          <BlockStack gap="500">
            <Card>
              <BlockStack gap="400">
                <Text as="h2" variant="headingMd">Statistiques de parrainage</Text>
                <InlineStack gap="400">
                  <div>
                    <Text as="p" variant="bodyMd" tone="subdued">Total parrainages</Text>
                    <Text as="p" variant="headingLg">{referralStats.total}</Text>
                  </div>
                  <div>
                    <Text as="p" variant="bodyMd" tone="subdued">Complétés</Text>
                    <Text as="p" variant="headingLg">{referralStats.completed}</Text>
                  </div>
                  <div>
                    <Text as="p" variant="bodyMd" tone="subdued">En attente</Text>
                    <Text as="p" variant="headingLg">{referralStats.pending}</Text>
                  </div>
                  <div>
                    <Text as="p" variant="bodyMd" tone="subdued">Taux de conversion</Text>
                    <Text as="p" variant="headingLg">{referralStats.conversionRate.toFixed(1)}%</Text>
                  </div>
                </InlineStack>
              </BlockStack>
            </Card>

            <Card>
              <BlockStack gap="400">
                <Text as="h2" variant="headingMd">Aide</Text>
                <Text as="p" variant="bodyMd">
                  Le programme de parrainage permet à vos clients de recommander votre boutique à leurs amis.
                  Les parrains et les filleuls reçoivent des récompenses lorsque le parrainage est validé.
                </Text>
                <Text as="p" variant="bodyMd">
                  Un parrainage est validé lorsque le filleul effectue son premier achat atteignant le montant minimum défini.
                </Text>
              </BlockStack>
            </Card>
          </BlockStack>
        </Layout.Section>
      </Layout>
      {toastMarkup}
    </AdminLayout>
    </Frame>
  );
}
