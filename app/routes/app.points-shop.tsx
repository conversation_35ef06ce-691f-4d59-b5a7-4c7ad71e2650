import { json, type ActionFunctionArgs } from "@remix-run/node";
import { useLoaderData, useSubmit } from "@remix-run/react";
import {
  Layout,
  Card,
  ResourceList,
  ResourceItem,
  Text,
  Button,
  Modal,
  FormLayout,
  TextField,
  Select,
  Thumbnail,
  BlockStack,
  Badge,
} from "@shopify/polaris";
import { useState } from "react";
import { AdminLayout } from "../components/Layout/AdminLayout";

export const loader = async () => {
  // TODO: Implement real data fetching
  const mockProducts = [
    {
      id: "1",
      title: "Bon d'achat 5€",
      pointsCost: 500,
      type: "coupon",
      image: "https://cdn.shopify.com/s/files/1/0/0/1/products/gift-card.png",
      status: "active",
    },
    {
      id: "2",
      title: "Livraison gratuite",
      pointsCost: 1000,
      type: "shipping",
      image: "https://cdn.shopify.com/s/files/1/0/0/1/products/shipping.png",
      status: "active",
    },
    {
      id: "3",
      title: "Produit exclusif VIP",
      pointsCost: 2500,
      type: "product",
      image: "https://cdn.shopify.com/s/files/1/0/0/1/products/vip-product.png",
      status: "draft",
    },
  ];

  return json({ products: mockProducts });
};

export const action = async ({ request }: ActionFunctionArgs) => {
  const formData = await request.formData();
  const data = Object.fromEntries(formData);
  
  // TODO: Implement real data saving
  console.log("Product to save:", data);
  
  return json({ success: true });
};

export default function AdminPointsShop() {
  const { products } = useLoaderData<typeof loader>();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const submit = useSubmit();

  const handleSubmit = (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    submit(event.currentTarget);
    setIsModalOpen(false);
  };

  const rewardTypes = [
    { label: "Bon d'achat", value: "coupon" },
    { label: "Livraison gratuite", value: "shipping" },
    { label: "Produit physique", value: "product" },
    { label: "Réduction pourcentage", value: "percentage" },
  ];

  return (
    <AdminLayout title="Boutique de points">
      <Layout>
        <Layout.Section>
          <Card>
            <BlockStack gap="400">
              <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center", padding: "16px 0" }}>
                <Text as="h2" variant="headingMd">Récompenses disponibles</Text>
                <Button variant="primary" onClick={() => setIsModalOpen(true)}>
                  Ajouter une récompense
                </Button>
              </div>

              <ResourceList
                resourceName={{ singular: "récompense", plural: "récompenses" }}
                items={products}
                renderItem={(item) => (
                  <ResourceItem
                    id={item.id}
                    onClick={() => {}}
                    media={
                      <Thumbnail
                        source={item.image}
                        alt={item.title}
                        size="small"
                      />
                    }
                    accessibilityLabel={`Voir les détails de ${item.title}`}
                  >
                    <BlockStack gap="200">
                      <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}>
                        <Text as="h3" variant="headingSm">{item.title}</Text>
                        <Badge tone={item.status === "active" ? "success" : "info"}>
                          {item.status === "active" ? "Actif" : "Brouillon"}
                        </Badge>
                      </div>
                      <Text as="p" variant="bodyMd">
                        {item.pointsCost} points
                      </Text>
                      <Text as="p" variant="bodySm">
                        Type: {rewardTypes.find(t => t.value === item.type)?.label}
                      </Text>
                    </BlockStack>
                  </ResourceItem>
                )}
              />
            </BlockStack>
          </Card>
        </Layout.Section>

        <Modal
          open={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          title="Nouvelle récompense"
        >
          <Modal.Section>
            <form onSubmit={handleSubmit}>
              <FormLayout>
                <TextField
                  label="Titre de la récompense"
                  type="text"
                  name="title"
                  autoComplete="off"
                />
                <TextField
                  label="Coût en points"
                  type="number"
                  name="pointsCost"
                  autoComplete="off"
                />
                <Select
                  label="Type de récompense"
                  options={rewardTypes}
                  name="type"
                />
                <TextField
                  label="Image URL"
                  type="url"
                  name="image"
                  autoComplete="off"
                />
                <Select
                  label="Statut"
                  options={[
                    { label: "Actif", value: "active" },
                    { label: "Brouillon", value: "draft" },
                  ]}
                  name="status"
                />
                <Button submit variant="primary">
                  Créer la récompense
                </Button>
              </FormLayout>
            </form>
          </Modal.Section>
        </Modal>
      </Layout>
    </AdminLayout>
  );
} 