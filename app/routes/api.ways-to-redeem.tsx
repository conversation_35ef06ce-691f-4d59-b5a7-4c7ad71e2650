import type { LoaderFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";
import { getWaysToRedeem } from "app/models/WayToRedeem.server";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const url = new URL(request.url);
  const shop = url.searchParams.get("shop");

  // Vérifications de sécurité
  if (!shop) {
    return json({ error: "Shop parameter required" }, { status: 400 });
  }

  try {
    // Récupérer les façons d'échanger des points actives
    const waysToRedeem = await getWaysToRedeem(shop);
    
    // Filtrer seulement les actives et formater pour l'interface client
    const activeWays = waysToRedeem
      .filter(way => way.isActive)
      .map(way => ({
        id: way.id,
        name: way.name,
        description: way.description,
        redeemType: way.redeemType,
        redeemValue: way.redeemValue,
        pointsCost: way.pointsCost,
        icon: way.icon
      }))
      .sort((a, b) => a.pointsCost - b.pointsCost); // Trier par coût croissant

    return json(activeWays, {
      headers: {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "GET",
        "Access-Control-Allow-Headers": "Content-Type",
      },
    });
  } catch (error) {
    console.error("Error fetching ways to redeem:", error);
    return json({ error: "Internal server error" }, { status: 500 });
  }
};

// Gérer les requêtes OPTIONS pour CORS
export const options = () => {
  return new Response(null, {
    status: 200,
    headers: {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "GET, OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type",
    },
  });
};
