import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import {
  Page,
  Layout,
  Card,
  Text,
  BlockStack,
  Grid,
  ProgressBar
} from "@shopify/polaris";
import { AdminLayout } from "app/components/Layout/AdminLayout";
import { authenticate } from "app/shopify.server";
import { getCustomerStats, getCustomersWithPointsLast30Days } from "app/models/Customer.server";
import db from "app/db.server";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const { session } = await authenticate.admin(request);
  const shop = session.shop;

  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

  // Statistiques des membres du programme
  const customerStats = await getCustomerStats(shop);
  const membersLast30Days = await getCustomersWithPointsLast30Days(shop);

  // Points de transaction sur 30 jours
  const pointsTransactions = await db.pointsHistory.aggregate({
    where: {
      customer: { shop },
      timestamp: { gte: thirtyDaysAgo }
    },
    _sum: { points: true },
    _count: { id: true }
  });

  // Achats de parrainage sur 30 jours
  const referralPurchases = await db.referral.count({
    where: {
      shop,
      status: "completed",
      completedAt: { gte: thirtyDaysAgo }
    }
  });

  // Données pour les mini graphiques (simulation)
  const generateTrendData = (value: number) => {
    // Génère des données de tendance simulées
    const data = [];
    for (let i = 0; i < 30; i++) {
      const variation = Math.random() * 0.3 - 0.15; // ±15% de variation
      const dayValue = Math.max(0, value * (1 + variation));
      data.push(dayValue);
    }
    return data;
  };

  return json({
    customerStats,
    membersLast30Days,
    pointsTransactions: {
      total: pointsTransactions._sum.points || 0,
      count: pointsTransactions._count || 0
    },
    referralPurchases,
    trends: {
      members: generateTrendData(membersLast30Days),
      points: generateTrendData(pointsTransactions._sum.points || 0),
      referrals: generateTrendData(referralPurchases)
    }
  });
};

// Composant pour afficher un mini graphique de tendance
function TrendChart({ data, color = "#2E7D32" }: { data: number[], color?: string }) {
  if (!data || data.length === 0) {
    return <div style={{ height: "40px", display: "flex", alignItems: "center" }}>
      <div style={{ width: "100%", height: "2px", backgroundColor: "#E1E3E5" }}></div>
    </div>;
  }

  const max = Math.max(...data);
  const min = Math.min(...data);
  const range = max - min || 1;

  const points = data.map((value, index) => {
    const x = (index / (data.length - 1)) * 100;
    const y = 100 - ((value - min) / range) * 100;
    return `${x},${y}`;
  }).join(' ');

  return (
    <div style={{ height: "40px", width: "100%" }}>
      <svg width="100%" height="40" viewBox="0 0 100 100" preserveAspectRatio="none">
        <polyline
          fill="none"
          stroke={color}
          strokeWidth="2"
          points={points}
        />
      </svg>
    </div>
  );
}

export default function Analytics() {
  const { 
    customerStats, 
    membersLast30Days, 
    pointsTransactions, 
    referralPurchases,
    trends 
  } = useLoaderData<typeof loader>();

  return (
    <AdminLayout title="Analyses">
      <Layout>
        <Layout.Section>
          <BlockStack gap="500">
            <Text as="h1" variant="headingLg">
              Analyses du programme de fidélité
            </Text>

            <Grid>
              {/* Membres du programme */}
              <Grid.Cell columnSpan={{ xs: 6, sm: 3, md: 2, lg: 4 }}>
                <Card>
                  <BlockStack gap="400">
                    <div style={{ display: "flex", justifyContent: "space-between", alignItems: "flex-start" }}>
                      <BlockStack gap="200">
                        <Text as="h3" variant="headingMd">Membres du programme</Text>
                        <Text as="p" variant="headingLg" fontWeight="bold">
                          {membersLast30Days}
                        </Text>
                        <Text as="p" variant="bodyMd" tone="subdued">
                          Clients avec des points sur 30 jours
                        </Text>
                      </BlockStack>
                    </div>
                    <TrendChart data={trends.members} color="#2E7D32" />
                    <Text as="p" variant="bodyMd" tone="subdued">
                      Évolution du nombre de membres actifs ayant des points dans leur compte.
                      Inclut les nouveaux membres et ceux qui ont gagné des points récemment.
                    </Text>
                  </BlockStack>
                </Card>
              </Grid.Cell>

              {/* Points de transaction */}
              <Grid.Cell columnSpan={{ xs: 6, sm: 3, md: 2, lg: 4 }}>
                <Card>
                  <BlockStack gap="400">
                    <div style={{ display: "flex", justifyContent: "space-between", alignItems: "flex-start" }}>
                      <BlockStack gap="200">
                        <Text as="h3" variant="headingMd">Points de transaction</Text>
                        <Text as="p" variant="headingLg" fontWeight="bold">
                          {pointsTransactions.total.toLocaleString()}
                        </Text>
                        <Text as="p" variant="bodyMd" tone="subdued">
                          Points échangés sur 30 jours
                        </Text>
                      </BlockStack>
                    </div>
                    <TrendChart data={trends.points} color="#1976D2" />
                    <Text as="p" variant="bodyMd" tone="subdued">
                      Total des points gagnés et dépensés par vos clients.
                      Comprend les points d'achat, de parrainage et d'échange de récompenses.
                    </Text>
                  </BlockStack>
                </Card>
              </Grid.Cell>

              {/* Achats de parrainage */}
              <Grid.Cell columnSpan={{ xs: 6, sm: 3, md: 2, lg: 4 }}>
                <Card>
                  <BlockStack gap="400">
                    <div style={{ display: "flex", justifyContent: "space-between", alignItems: "flex-start" }}>
                      <BlockStack gap="200">
                        <Text as="h3" variant="headingMd">Achats de parrainage</Text>
                        <Text as="p" variant="headingLg" fontWeight="bold">
                          {referralPurchases}
                        </Text>
                        <Text as="p" variant="bodyMd" tone="subdued">
                          Parrainages validés sur 30 jours
                        </Text>
                      </BlockStack>
                    </div>
                    <TrendChart data={trends.referrals} color="#F57C00" />
                    <Text as="p" variant="bodyMd" tone="subdued">
                      Nombre de nouveaux clients acquis grâce au programme de parrainage.
                      Un parrainage est validé quand le filleul effectue son premier achat.
                    </Text>
                  </BlockStack>
                </Card>
              </Grid.Cell>
            </Grid>

            {/* Statistiques globales */}
            <Card>
              <BlockStack gap="400">
                <Text as="h2" variant="headingMd">Statistiques globales</Text>
                
                <Grid>
                  <Grid.Cell columnSpan={{ xs: 6, sm: 3, md: 2, lg: 3 }}>
                    <BlockStack gap="200">
                      <Text as="h4" variant="headingSm">Total des clients</Text>
                      <Text as="p" variant="headingMd" fontWeight="bold">
                        {customerStats.totalCustomers}
                      </Text>
                      <Text as="p" variant="bodyMd" tone="subdued">
                        Tous les clients de votre boutique
                      </Text>
                    </BlockStack>
                  </Grid.Cell>

                  <Grid.Cell columnSpan={{ xs: 6, sm: 3, md: 2, lg: 3 }}>
                    <BlockStack gap="200">
                      <Text as="h4" variant="headingSm">Membres inscrits</Text>
                      <Text as="p" variant="headingMd" fontWeight="bold">
                        {customerStats.members}
                      </Text>
                      <Text as="p" variant="bodyMd" tone="subdued">
                        Clients inscrits au programme
                      </Text>
                      <ProgressBar 
                        progress={(customerStats.members / customerStats.totalCustomers) * 100} 
                        size="small"
                      />
                    </BlockStack>
                  </Grid.Cell>

                  <Grid.Cell columnSpan={{ xs: 6, sm: 3, md: 2, lg: 3 }}>
                    <BlockStack gap="200">
                      <Text as="h4" variant="headingSm">Clients invités</Text>
                      <Text as="p" variant="headingMd" fontWeight="bold">
                        {customerStats.guests}
                      </Text>
                      <Text as="p" variant="bodyMd" tone="subdued">
                        Clients non inscrits au programme
                      </Text>
                      <ProgressBar 
                        progress={(customerStats.guests / customerStats.totalCustomers) * 100} 
                        size="small"
                      />
                    </BlockStack>
                  </Grid.Cell>

                  <Grid.Cell columnSpan={{ xs: 6, sm: 3, md: 2, lg: 3 }}>
                    <BlockStack gap="200">
                      <Text as="h4" variant="headingSm">Clients avec points</Text>
                      <Text as="p" variant="headingMd" fontWeight="bold">
                        {customerStats.customersWithPoints}
                      </Text>
                      <Text as="p" variant="bodyMd" tone="subdued">
                        Clients ayant un solde de points
                      </Text>
                      <ProgressBar 
                        progress={(customerStats.customersWithPoints / customerStats.totalCustomers) * 100} 
                        size="small"
                      />
                    </BlockStack>
                  </Grid.Cell>
                </Grid>
              </BlockStack>
            </Card>
          </BlockStack>
        </Layout.Section>
      </Layout>
    </AdminLayout>
  );
}
