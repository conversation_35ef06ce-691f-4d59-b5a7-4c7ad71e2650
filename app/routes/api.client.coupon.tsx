import { json, type ActionFunctionArgs } from "@remix-run/node";
import { createLoyaltyCoupon, calculateCouponValue } from "../services/loyaltyCoupons.server";

export const action = async ({ request }: ActionFunctionArgs) => {
  try {
    const formData = await request.formData();
    const shop = formData.get('shop') as string;
    const customerId = formData.get('customerId') as string;
    const pointsToSpend = parseInt(formData.get('pointsToSpend') as string);
    const wayToRedeemId = formData.get('wayToRedeemId') as string;

    if (!shop || !customerId || !pointsToSpend) {
      return json({ error: "Paramètres manquants" }, { status: 400 });
    }

    // Créer le coupon de fidélité
    const result = await createLoyaltyCoupon(request, {
      customerId,
      shop,
      pointsToSpend,
      wayToRedeemId
    });

    if (!result.success) {
      return json({ error: result.error }, { status: 400 });
    }

    return json({
      success: true,
      coupon: result.coupon,
      message: `Coupon de ${result.coupon?.value}€ créé avec succès !`
    });

  } catch (error) {
    console.error("Error in coupon creation:", error);
    return json({ error: "Erreur lors de la création du coupon" }, { status: 500 });
  }
};

// Route GET pour calculer la valeur d'un coupon
export const loader = async ({ request }: ActionFunctionArgs) => {
  try {
    const url = new URL(request.url);
    const shop = url.searchParams.get('shop');
    const points = url.searchParams.get('points');

    if (!shop || !points) {
      return json({ error: "Paramètres manquants" }, { status: 400 });
    }

    const pointsNumber = parseInt(points);
    const couponValue = await calculateCouponValue(shop, pointsNumber);

    return json({
      points: pointsNumber,
      value: couponValue,
      formatted: `${couponValue}€`
    });

  } catch (error) {
    console.error("Error calculating coupon value:", error);
    return json({ error: "Erreur lors du calcul" }, { status: 500 });
  }
};
