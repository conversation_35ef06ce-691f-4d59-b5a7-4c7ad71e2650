import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { authenticate } from "../shopify.server";
import { searchCustomers } from "../models/Customer.server";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  try {
    const { session } = await authenticate.admin(request);
    const url = new URL(request.url);
    const query = url.searchParams.get("q") || "";

    if (!query.trim()) {
      return json([]);
    }

    const customers = await searchCustomers(session.shop, query, 8);
    
    // Formater les résultats pour l'affichage
    const formattedResults = customers.map(customer => ({
      id: customer.id,
      customerId: customer.customerId,
      name: `${customer.firstName || ''} ${customer.lastName || ''}`.trim() || customer.email,
      email: customer.email,
      points: customer.points,
      type: customer.type,
      url: `/app/customers/${customer.id}`
    }));

    return json(formattedResults);
  } catch (error) {
    console.error("Error in search API:", error);
    return json([]);
  }
};
