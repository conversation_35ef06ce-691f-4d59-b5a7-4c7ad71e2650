import { json, redirect } from "@remix-run/node";
import type { ActionFunction, LoaderFunction } from "@remix-run/node";
import { getProgramSettings, updateProgramSettings, toggleProgramStatus } from "../models/ProgramSettings.server";

export const loader: LoaderFunction = async () => {
  const settings = await getProgramSettings();
  return json(settings || {});
};

export const action: ActionFunction = async ({ request }) => {
  const formData = await request.formData();
  const action = formData.get("action");

  switch (action) {
    case "toggle":
      await toggleProgramStatus();
      return redirect("/app/program");

    case "update":
      // Récupérer les settings actuels
      const currentSettings = await getProgramSettings();

      // Construire les nouvelles données en gardant les valeurs existantes
      const data = {
        name: formData.get("name") as string || currentSettings?.name || "",
        description: formData.get("description") as string || currentSettings?.description || "",
      };

      // Si un seul champ est envoyé, garder l'autre valeur
      if (formData.get("name") && !formData.get("description")) {
        data.description = currentSettings?.description || "";
      }
      if (formData.get("description") && !formData.get("name")) {
        data.name = currentSettings?.name || "";
      }

      await updateProgramSettings(data);
      return json({ success: true });

    default:
      return json({ error: "Action non valide" }, { status: 400 });
  }
};
