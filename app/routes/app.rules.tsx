import { json, type ActionFunctionArgs } from "@remix-run/node";
import { useLoaderData, useSubmit } from "@remix-run/react";
import {
  Layout,
  Card,
  FormLayout,
  TextField,
  Button,
  BlockStack,
  Text,
} from "@shopify/polaris";
import { AdminLayout } from "../components/Layout/AdminLayout";
import db from "../db.server";

export const loader = async () => {
  // TODO: Implement real data fetching
  const mockSettings = {
    earningRate: "1",
    redemptionRate: "0.01",
    vipThreshold: "1000",
    referralPoints: "100",
    birthdayPoints: "250",
  };

  return json({ settings: mockSettings });
};

export const action = async ({ request }: ActionFunctionArgs) => {
  const formData = await request.formData();
  const settings = Object.fromEntries(formData);
  
  // TODO: Implement real data saving
  console.log("Settings to save:", settings);
  
  return json({ success: true });
};

export default function AdminRules() {
  const { settings } = useLoaderData<typeof loader>();
  const submit = useSubmit();

  const handleSubmit = (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    submit(event.currentTarget);
  };

  return (
    <AdminLayout title="Règles de points">
      <Layout>
        <Layout.Section>
          <Card>
            <BlockStack gap="400">
              <Text as="h2" variant="headingMd">Configuration des points</Text>
              <form onSubmit={handleSubmit}>
                <FormLayout>
                  <TextField
                    label="Taux de gain (points par euro)"
                    type="number"
                    name="earningRate"
                    value={settings.earningRate}
                    autoComplete="off"
                    step={0.01}
                  />
                  <TextField
                    label="Taux de conversion (euro par point)"
                    type="number"
                    name="redemptionRate"
                    value={settings.redemptionRate}
                    autoComplete="off"
                    step={0.01}
                  />
                  <TextField
                    label="Seuil VIP (points)"
                    type="number"
                    name="vipThreshold"
                    value={settings.vipThreshold}
                    autoComplete="off"
                  />
                  <TextField
                    label="Points de parrainage"
                    type="number"
                    name="referralPoints"
                    value={settings.referralPoints}
                    autoComplete="off"
                  />
                  <TextField
                    label="Points d'anniversaire"
                    type="number"
                    name="birthdayPoints"
                    value={settings.birthdayPoints}
                    autoComplete="off"
                  />
                  <Button submit variant="primary">Enregistrer</Button>
                </FormLayout>
              </form>
            </BlockStack>
          </Card>
        </Layout.Section>
      </Layout>
    </AdminLayout>
  );
} 