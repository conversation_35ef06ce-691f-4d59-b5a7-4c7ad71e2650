import { json } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import {
  Layout,
  Card,
  DataTable,
  Filters,
  Text,
  BlockStack,
  Button,
  ButtonGroup,
  RangeSlider,
} from "@shopify/polaris";
import { useState } from "react";
import { AdminLayout } from "../components/Layout/AdminLayout";
import { RangeSliderValue } from "@shopify/polaris/build/ts/src/components/RangeSlider/types";

export const loader = async () => {
  // TODO: Implement real data fetching
  const mockHistory = {
    transactions: [
      ["#123", "<PERSON>", "Achat", "+50", "2024-03-20", "Commande #A123"],
      ["#124", "Marie Martin", "Utilisation", "-100", "2024-03-20", "Réduction 5€"],
      ["#125", "<PERSON>", "Parrainage", "+100", "2024-03-19", "Parrainage de Marie"],
      ["#126", "<PERSON>", "Anniversaire", "+250", "2024-03-19", "Bonus anniversaire"],
      ["#127", "<PERSON>", "VIP", "+500", "2024-03-18", "Passage VIP"],
    ],
    total: 150,
  };

  return json({ history: mockHistory });
};

export default function AdminHistory() {
  const { history } = useLoaderData<typeof loader>();
  const [selectedDateRange, setSelectedDateRange] = useState([0, 30]);
  const [queryValue, setQueryValue] = useState("");

  const handleFiltersQueryChange = (value: string) => {
    setQueryValue(value);
  };

  const handleDateRangeChange = (value: [number, number]) => {
    setSelectedDateRange(value);
  };

  return (
    <AdminLayout title="Historique">
      <Layout>
        <Layout.Section>
          <Card>
            <BlockStack gap="400">
              <div style={{ padding: "16px 0" }}>
                <Text as="h2" variant="headingMd">Historique des transactions</Text>
              </div>

              <div style={{ padding: "16px 0" }}>
                <ButtonGroup>
                  <Button pressed>7 derniers jours</Button>
                  <Button>30 derniers jours</Button>
                  <Button>90 derniers jours</Button>
                </ButtonGroup>
              </div>

              <div style={{ padding: "0 16px" }}>
                <RangeSlider
                  label="Période personnalisée (jours)"
                  value={selectedDateRange as RangeSliderValue}
                  min={0}
                  max={90}
                  onChange={handleDateRangeChange}
                />
              </div>

              <Filters
                filters={[]}
                onClearAll={() => {}}
                queryValue={queryValue}
                queryPlaceholder="Rechercher par client ou type"
                onQueryChange={handleFiltersQueryChange}
                onQueryClear={() => setQueryValue("")}
              />

              <DataTable
                columnContentTypes={[
                  "text",
                  "text",
                  "text",
                  "numeric",
                  "text",
                  "text",
                ]}
                headings={[
                  "ID",
                  "Client",
                  "Type",
                  "Points",
                  "Date",
                  "Description",
                ]}
                rows={history.transactions}
                totals={["", "", "", `${history.total}`, "", ""]}
              />
            </BlockStack>
          </Card>
        </Layout.Section>
      </Layout>
    </AdminLayout>
  );
} 