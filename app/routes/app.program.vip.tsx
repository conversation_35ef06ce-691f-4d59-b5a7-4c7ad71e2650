import { json, type ActionFunctionArgs } from "@remix-run/node";
import { useLoaderData, useSubmit } from "@remix-run/react";
import {
  Layout,
  Card,
  FormLayout,
  TextField,
  Button,
  BlockStack,
  Text,
  Banner,
  Box,
  InlineStack,
  Icon,
  DataTable,
  Badge,
} from "@shopify/polaris";
import { StarIcon } from "@shopify/polaris-icons";
import { AdminLayout } from "app/components/Layout/AdminLayout";

export const loader = async () => {
  // TODO: Implémenter la récupération des données réelles
  const mockSettings = {
    active: true,
    levels: [
      {
        id: 1,
        name: "Bronze",
        threshold: "0",
        pointsMultiplier: "1",
        benefits: "Taux de base",
      },
      {
        id: 2,
        name: "Argent",
        threshold: "1000",
        pointsMultiplier: "1.25",
        benefits: "25% de points bonus",
      },
      {
        id: 3,
        name: "Or",
        threshold: "5000",
        pointsMultiplier: "1.5",
        benefits: "50% de points bonus\nLivraison gratuite",
      },
      {
        id: 4,
        name: "<PERSON><PERSON>",
        threshold: "10000",
        pointsMultiplier: "2",
        benefits: "Double points\nLivraison gratuite\nService client prioritaire",
      },
    ],
    retentionPeriod: "365",
    evaluationPeriod: "90",
  };

  return json({ settings: mockSettings });
};

export const action = async ({ request }: ActionFunctionArgs) => {
  const formData = await request.formData();
  const settings = Object.fromEntries(formData);
  
  // TODO: Implémenter la sauvegarde réelle des données
  console.log("Settings to save:", settings);
  
  return json({ success: true });
};

export default function ProgramVIP() {
  const { settings } = useLoaderData<typeof loader>();
  const submit = useSubmit();

  const handleSubmit = (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    submit(event.currentTarget);
  };

  const rows = settings.levels.map((level) => [
    <Text as="span" variant="bodyMd" fontWeight="bold">{level.name}</Text>,
    <Text as="span" variant="bodyMd">{level.threshold}€</Text>,
    <Text as="span" variant="bodyMd">x{level.pointsMultiplier}</Text>,
    <Text as="span" variant="bodyMd">{level.benefits.split('\n').join(', ')}</Text>,
    <Button size="slim">Modifier</Button>,
  ]);

  return (
    <AdminLayout title="Programme VIP">
      <Layout>
        <Layout.Section>
          <BlockStack gap="500">
            <Card>
              <BlockStack gap="400">
                <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}>
                  <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
                    <Text as="h2" variant="headingMd">État du programme</Text>
                    <Badge tone={settings.active ? "success" : "attention"}>
                      {settings.active ? "Actif" : "Inactif"}
                    </Badge>
                  </div>
                  <Button tone={settings.active ? "critical" : "success"}>
                    {settings.active ? "Désactiver" : "Activer"}
                  </Button>
                </div>
              </BlockStack>
            </Card>

            <Card>
              <BlockStack gap="400">
                <Text as="h2" variant="headingMd">Niveaux VIP</Text>
                <Box paddingBlockEnd="400">
                  <InlineStack gap="200" align="center">
                    <Icon source={StarIcon} />
                    <Text as="span" variant="bodyMd">
                      {settings.levels.length} niveaux configurés
                    </Text>
                  </InlineStack>
                </Box>
                <DataTable
                  columnContentTypes={["text", "text", "text", "text", "text"]}
                  headings={["Niveau", "Seuil (€)", "Multiplicateur", "Avantages", "Actions"]}
                  rows={rows}
                />
                <div style={{ paddingTop: "var(--p-space-400)" }}>
                  <Button>Ajouter un niveau</Button>
                </div>
              </BlockStack>
            </Card>

            <Card>
              <BlockStack gap="400">
                <Text as="h2" variant="headingMd">Périodes d'évaluation</Text>
                <form onSubmit={handleSubmit}>
                  <FormLayout>
                    <TextField
                      label="Période de rétention du statut (jours)"
                      type="number"
                      name="retentionPeriod"
                      value={settings.retentionPeriod}
                      autoComplete="off"
                      helpText="Durée pendant laquelle un client conserve son statut VIP"
                      min={0}
                    />
                    <TextField
                      label="Période d'évaluation (jours)"
                      type="number"
                      name="evaluationPeriod"
                      value={settings.evaluationPeriod}
                      autoComplete="off"
                      helpText="Fréquence de réévaluation du statut VIP des clients"
                      min={0}
                    />
                    <Button submit variant="primary">Enregistrer les modifications</Button>
                  </FormLayout>
                </form>
              </BlockStack>
            </Card>
          </BlockStack>
        </Layout.Section>

        <Layout.Section secondary>
          <BlockStack gap="500">
            <Card>
              <BlockStack gap="400">
                <Text as="h2" variant="headingMd">Statistiques</Text>
                <BlockStack gap="200">
                  <Text as="p" variant="bodyMd">
                    Clients Bronze : <Text as="span" variant="headingSm">0</Text>
                  </Text>
                  <Text as="p" variant="bodyMd">
                    Clients Argent : <Text as="span" variant="headingSm">0</Text>
                  </Text>
                  <Text as="p" variant="bodyMd">
                    Clients Or : <Text as="span" variant="headingSm">0</Text>
                  </Text>
                  <Text as="p" variant="bodyMd">
                    Clients Platine : <Text as="span" variant="headingSm">0</Text>
                  </Text>
                </BlockStack>
              </BlockStack>
            </Card>

            <Card>
              <BlockStack gap="400">
                <Text as="h2" variant="headingMd">Aide</Text>
                <Text as="p" variant="bodyMd">
                  Le programme VIP permet de récompenser vos meilleurs clients avec des avantages exclusifs.
                  Les clients sont automatiquement promus en fonction de leurs dépenses totales.
                </Text>
                <Text as="p" variant="bodyMd">
                  Le statut VIP est réévalué périodiquement pour maintenir l'engagement des clients.
                </Text>
              </BlockStack>
            </Card>
          </BlockStack>
        </Layout.Section>
      </Layout>
    </AdminLayout>
  );
} 