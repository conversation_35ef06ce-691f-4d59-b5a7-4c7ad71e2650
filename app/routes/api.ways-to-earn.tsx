import type { LoaderFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";
import { getWaysToEarn } from "app/models/WayToEarn.server";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const url = new URL(request.url);
  const shop = url.searchParams.get("shop");

  // Vérifications de sécurité
  if (!shop) {
    return json({ error: "Shop parameter required" }, { status: 400 });
  }

  try {
    // Récupérer les façons de gagner des points actives
    const waysToEarn = await getWaysToEarn(shop);
    
    // Filtrer seulement les actives et formater pour l'interface client
    const activeWays = waysToEarn
      .filter(way => way.isActive)
      .map(way => ({
        id: way.id,
        name: way.name,
        description: way.description,
        actionType: way.actionType,
        earningType: way.earningType,
        earningValue: way.earningValue,
        icon: way.icon
      }));

    return json(activeWays, {
      headers: {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "GET",
        "Access-Control-Allow-Headers": "Content-Type",
      },
    });
  } catch (error) {
    console.error("Error fetching ways to earn:", error);
    return json({ error: "Internal server error" }, { status: 500 });
  }
};

// Gérer les requêtes OPTIONS pour CORS
export const options = () => {
  return new Response(null, {
    status: 200,
    headers: {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "GET, OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type",
    },
  });
};
