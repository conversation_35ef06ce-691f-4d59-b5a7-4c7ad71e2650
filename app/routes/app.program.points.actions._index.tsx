import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData, Link } from "@remix-run/react";
import {
  Page,
  Layout,
  Card,
  Button,
  Text,
  BlockStack,
  DataTable,
  Badge,
  ButtonGroup,
  Frame
} from "@shopify/polaris";
import { AdminLayout } from "app/components/Layout/AdminLayout";
import { authenticate } from "app/shopify.server";
import { getWaysToEarn } from "app/models/WayToEarn.server";
import { getWaysToRedeem } from "app/models/WayToRedeem.server";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const { session } = await authenticate.admin(request);

  const [waysToEarn, waysToRedeem] = await Promise.all([
    getWaysToEarn(session.shop),
    getWaysToRedeem(session.shop)
  ]);

  return json({
    waysToEarn,
    waysToRedeem
  });
};

export default function ActionsIndex() {
  const { waysToEarn, waysToRedeem } = useLoaderData<typeof loader>();

  const earnRows = waysToEarn.map((way) => [
    way.icon,
    way.name,
    way.description,
    way.isActive ? <Badge tone="success">Actif</Badge> : <Badge tone="critical">Inactif</Badge>,
    <ButtonGroup>
      <Button
        size="micro"
        url={`/app/program/points/actions/earn/${way.id}`}
      >
        Modifier
      </Button>
    </ButtonGroup>
  ]);

  const redeemRows = waysToRedeem.map((way) => [
    way.icon,
    way.name,
    way.description,
    `${way.pointsCost} points`,
    way.isActive ? <Badge tone="success">Actif</Badge> : <Badge tone="critical">Inactif</Badge>,
    <ButtonGroup>
      <Button
        size="micro"
        url={`/app/program/points/actions/redeem/${way.id}`}
      >
        Modifier
      </Button>
    </ButtonGroup>
  ]);

  return (
    <Frame>
      <AdminLayout>
        <Page
          title="Gestion des actions"
          backAction={{
            content: "Retour à la configuration",
            url: "/app/program/points"
          }}
        >
          <Layout>
            <Layout.Section>
              <Card>
                <BlockStack gap="400">
                  <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}>
                    <Text as="h2" variant="headingMd">Façons de gagner des points</Text>
                    <Button
                      variant="primary"
                      url="/app/program/points/actions/earn/new"
                    >
                      Ajouter une façon de gagner
                    </Button>
                  </div>

                  <Text as="p" variant="bodyMd" tone="subdued">
                    Configurez les différentes façons dont vos clients peuvent gagner des points dans votre programme de fidélité.
                  </Text>

                  {earnRows.length > 0 ? (
                    <DataTable
                      columnContentTypes={["text", "text", "text", "text", "text"]}
                      headings={["Icône", "Nom", "Description", "Statut", "Actions"]}
                      rows={earnRows}
                    />
                  ) : (
                    <div style={{ textAlign: "center", padding: "40px" }}>
                      <Text as="p" variant="bodyMd" tone="subdued">
                        Aucune façon de gagner des points configurée.
                      </Text>
                      <div style={{ marginTop: "16px" }}>
                        <Button
                          variant="primary"
                          url="/app/program/points/actions/earn/new"
                        >
                          Créer la première façon de gagner
                        </Button>
                      </div>
                    </div>
                  )}
                </BlockStack>
              </Card>
            </Layout.Section>

            <Layout.Section>
              <Card>
                <BlockStack gap="400">
                  <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}>
                    <Text as="h2" variant="headingMd">Façons d'échanger des points</Text>
                    <Button
                      variant="primary"
                      url="/app/program/points/actions/redeem/new"
                    >
                      Ajouter une façon d'échanger
                    </Button>
                  </div>

                  <Text as="p" variant="bodyMd" tone="subdued">
                    Configurez les différentes façons dont vos clients peuvent utiliser leurs points pour obtenir des récompenses.
                  </Text>

                  {redeemRows.length > 0 ? (
                    <DataTable
                      columnContentTypes={["text", "text", "text", "text", "text", "text"]}
                      headings={["Icône", "Nom", "Description", "Coût", "Statut", "Actions"]}
                      rows={redeemRows}
                    />
                  ) : (
                    <div style={{ textAlign: "center", padding: "40px" }}>
                      <Text as="p" variant="bodyMd" tone="subdued">
                        Aucune façon d'échanger des points configurée.
                      </Text>
                      <div style={{ marginTop: "16px" }}>
                        <Button
                          variant="primary"
                          url="/app/program/points/actions/redeem/new"
                        >
                          Créer la première façon d'échanger
                        </Button>
                      </div>
                    </div>
                  )}
                </BlockStack>
              </Card>
            </Layout.Section>
          </Layout>
        </Page>
      </AdminLayout>
    </Frame>
  );
}
