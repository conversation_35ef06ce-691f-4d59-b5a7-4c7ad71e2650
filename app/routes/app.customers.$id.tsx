import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData, Link } from "@remix-run/react";
import {
  Page,
  Layout,
  Card,
  DataTable,
  Badge,
  Text,
  BlockStack,
  Button,
  ButtonGroup,
  Tabs,
  Grid,
  Icon
} from "@shopify/polaris";
import { useState, useCallback } from "react";
import { ArrowLeftIcon, ViewIcon } from "@shopify/polaris-icons";
import { AdminLayout } from "app/components/Layout/AdminLayout";
import { authenticate } from "app/shopify.server";
import { getCustomerById } from "app/models/Customer.server";

export const loader = async ({ request, params }: LoaderFunctionArgs) => {
  const { session } = await authenticate.admin(request);
  const customerId = params.id;

  if (!customerId) {
    throw new Response("Customer ID required", { status: 400 });
  }

  const customer = await getCustomerById(customerId, session.shop);

  if (!customer) {
    throw new Response("Customer not found", { status: 404 });
  }

  return json({ customer, shop: session.shop });
};

export default function CustomerDetail() {
  const { customer, shop } = useLoaderData<typeof loader>();
  const [selectedTab, setSelectedTab] = useState(0);

  const handleTabChange = useCallback((selectedTabIndex: number) => {
    setSelectedTab(selectedTabIndex);
  }, []);

  const formatCustomerName = () => {
    const firstName = customer.firstName || "";
    const lastName = customer.lastName || "";
    if (firstName || lastName) {
      return `${firstName} ${lastName}`.trim();
    }
    return customer.email || `Client ${customer.customerId}`;
  };

  const shopifyUrl = `https://${shop}/admin/customers/${customer.customerId}`;

  // Préparer les données pour les onglets
  const pointsRows = customer.history
    .filter((h: any) => h.action !== "referral")
    .map((history: any) => [
      history.action === "earn" ? "Gagné" : 
      history.action === "redeem" ? "Échangé" : 
      history.action === "signup" ? "Inscription" : history.action,
      `${history.points > 0 ? "+" : ""}${history.points} points`,
      new Date(history.timestamp).toLocaleDateString("fr-FR")
    ]);

  const referralRows = customer.referrals.map((referral: any) => [
    referral.referred ? 
      `${referral.referred.firstName || ""} ${referral.referred.lastName || ""}`.trim() || 
      referral.referred.email || "Client anonyme" : 
      "En attente",
    <Badge tone={referral.status === "completed" ? "success" : "warning"}>
      {referral.status === "completed" ? "Validé" : "En attente"}
    </Badge>,
    "—", // Total commande (à implémenter)
    new Date(referral.createdAt).toLocaleDateString("fr-FR")
  ]);

  const rewardRows = []; // À implémenter avec le modèle Reward

  const orderRows = customer.orders.map((order: any) => [
    order.orderId,
    `${order.total.toFixed(2)}€`,
    <Badge tone={order.paymentStatus === "paid" ? "success" : "warning"}>
      {order.paymentStatus === "paid" ? "Payé" : order.paymentStatus}
    </Badge>,
    new Date(order.createdAt).toLocaleDateString("fr-FR")
  ]);

  const tabs = [
    {
      id: "points",
      content: "Points",
      panelID: "points-panel"
    },
    {
      id: "referrals", 
      content: "Parrainages",
      panelID: "referrals-panel"
    },
    {
      id: "rewards",
      content: "Récompenses", 
      panelID: "rewards-panel"
    }
  ];

  const renderTabContent = () => {
    switch (selectedTab) {
      case 0:
        return (
          <DataTable
            columnContentTypes={["text", "text", "text"]}
            headings={["Action", "Points", "Date"]}
            rows={pointsRows}
          />
        );
      case 1:
        return (
          <DataTable
            columnContentTypes={["text", "text", "text", "text"]}
            headings={["Filleul", "Statut", "Commande total", "Date"]}
            rows={referralRows}
          />
        );
      case 2:
        return (
          <DataTable
            columnContentTypes={["text", "text", "text", "text"]}
            headings={["Récompense", "Code", "Statut", "Date"]}
            rows={rewardRows.length > 0 ? rewardRows : [["Aucune récompense échangée", "—", "—", "—"]]}
          />
        );
      default:
        return null;
    }
  };

  return (
    <AdminLayout title={formatCustomerName()}>
      <Layout>
        <Layout.Section>
          <div style={{ marginBottom: "20px", display: "flex", justifyContent: "space-between", alignItems: "center" }}>
            <div style={{ display: "flex", alignItems: "center", gap: "12px" }}>
              <Link to="/app/customers">
                <Button icon={ArrowLeftIcon} variant="tertiary">
                  Retour
                </Button>
              </Link>
              <Text as="h1" variant="headingLg">
                {formatCustomerName()}
              </Text>
            </div>
            <Button 
              icon={ViewIcon} 
              variant="primary"
              url={shopifyUrl}
              external
            >
              Voir dans Shopify
            </Button>
          </div>
        </Layout.Section>

        <Layout.Section>
          <Grid>
            <Grid.Cell columnSpan={{ xs: 6, sm: 3, md: 4, lg: 8 }}>
              <BlockStack gap="400">
                {/* Informations client */}
                <Card>
                  <BlockStack gap="300">
                    <Text as="h2" variant="headingMd">Informations client</Text>
                    <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
                      <Text as="span" variant="bodyMd">Type:</Text>
                      <Badge tone={customer.type === "member" ? "success" : "info"}>
                        {customer.type === "member" ? "Membre" : "Invité"}
                      </Badge>
                    </div>
                    {customer.email && (
                      <Text as="p" variant="bodyMd">
                        <strong>Email:</strong> {customer.email}
                      </Text>
                    )}
                    <Text as="p" variant="bodyMd">
                      <strong>Inscrit le:</strong> {new Date(customer.joinedAt).toLocaleDateString("fr-FR")}
                    </Text>
                  </BlockStack>
                </Card>

                {/* Activité */}
                <Card>
                  <BlockStack gap="400">
                    <Text as="h2" variant="headingMd">Activité</Text>
                    <Tabs tabs={tabs} selected={selectedTab} onSelect={handleTabChange}>
                      <div style={{ padding: "16px 0" }}>
                        {renderTabContent()}
                      </div>
                    </Tabs>
                  </BlockStack>
                </Card>

                {/* Commandes */}
                <Card>
                  <BlockStack gap="400">
                    <Text as="h2" variant="headingMd">Commandes</Text>
                    <DataTable
                      columnContentTypes={["text", "text", "text", "text"]}
                      headings={["ID Commande", "Total", "Statut", "Date"]}
                      rows={orderRows.length > 0 ? orderRows : [["Aucune commande", "—", "—", "—"]]}
                    />
                  </BlockStack>
                </Card>
              </BlockStack>
            </Grid.Cell>

            <Grid.Cell columnSpan={{ xs: 6, sm: 3, md: 2, lg: 4 }}>
              <BlockStack gap="400">
                {/* Points */}
                <Card>
                  <BlockStack gap="300">
                    <Text as="h3" variant="headingMd">Points</Text>
                    <Text as="p" variant="headingLg" fontWeight="bold">
                      {customer.points} points
                    </Text>
                    <Text as="p" variant="bodyMd" tone="subdued">
                      Solde actuel
                    </Text>
                  </BlockStack>
                </Card>

                {/* Statistiques */}
                <Card>
                  <BlockStack gap="300">
                    <Text as="h3" variant="headingMd">Statistiques</Text>
                    <Text as="p" variant="bodyMd">
                      <strong>Total dépensé:</strong> {customer.totalSpent.toFixed(2)}€
                    </Text>
                    <Text as="p" variant="bodyMd">
                      <strong>Commandes:</strong> {customer.ordersCount}
                    </Text>
                    <Text as="p" variant="bodyMd">
                      <strong>Parrainages:</strong> {customer.referrals.filter((r: any) => r.status === "completed").length}
                    </Text>
                  </BlockStack>
                </Card>

                {/* Parrainage */}
                <Card>
                  <BlockStack gap="300">
                    <Text as="h3" variant="headingMd">Parrainage</Text>
                    <Text as="p" variant="bodyMd" tone="subdued">
                      Fonctionnalité à développer
                    </Text>
                    <Text as="p" variant="bodyMd" tone="subdued">
                      Code de parrainage et lien à afficher ici
                    </Text>
                  </BlockStack>
                </Card>
              </BlockStack>
            </Grid.Cell>
          </Grid>
        </Layout.Section>
      </Layout>
    </AdminLayout>
  );
}
