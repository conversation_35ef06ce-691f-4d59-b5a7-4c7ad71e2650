import type { LoaderFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";
import { getCustomerByShopifyId, getCustomerById } from "app/models/Customer.server";

export const loader = async ({ request, params }: LoaderFunctionArgs) => {
  const url = new URL(request.url);
  const shop = url.searchParams.get("shop");
  const customerId = params.customerId;

  // Vérifications de sécurité
  if (!shop) {
    return json({ error: "Shop parameter required" }, { status: 400 });
  }

  if (!customerId) {
    return json({ error: "Customer ID required" }, { status: 400 });
  }

  try {
    // D'abord, récupérer le client par son ID Shopify
    const basicCustomer = await getCustomerByShopifyId(customerId, shop);

    if (!basicCustomer) {
      // Si le client n'existe pas dans notre DB, créer un profil guest
      const guestCustomer = {
        id: null,
        customerId: customerId,
        firstName: null,
        lastName: null,
        email: null,
        type: "guest",
        points: 0,
        vipLevel: null,
        totalSpent: 0,
        ordersCount: 0,
        joinedAt: new Date(),
        recentHistory: [],
        referralsCount: 0
      };

      return json(guestCustomer, {
        headers: {
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Methods": "GET",
          "Access-Control-Allow-Headers": "Content-Type",
        },
      });
    }

    // Récupérer les données complètes avec relations
    const customer = await getCustomerById(basicCustomer.id, shop);

    if (!customer) {
      // Si le client n'existe pas dans notre DB, créer un profil guest
      const guestCustomer = {
        id: null,
        customerId: customerId,
        firstName: null,
        lastName: null,
        email: null,
        type: "guest",
        points: 0,
        vipLevel: null,
        totalSpent: 0,
        ordersCount: 0,
        joinedAt: new Date(),
        recentHistory: [],
        referralsCount: 0
      };

      return json(guestCustomer, {
        headers: {
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Methods": "GET",
          "Access-Control-Allow-Headers": "Content-Type",
        },
      });
    }

    // Calculer les statistiques supplémentaires
    const recentHistory = customer.history ? customer.history.slice(0, 5) : [];
    const referralsCount = customer.referrals ? customer.referrals.filter((r: any) => r.status === "completed").length : 0;

    // Retourner les données complètes pour l'embed
    const customerData = {
      id: customer.id,
      customerId: customer.customerId,
      firstName: customer.firstName,
      lastName: customer.lastName,
      email: customer.email,
      type: customer.type,
      points: customer.points,
      vipLevel: customer.vipLevel,
      totalSpent: customer.totalSpent,
      ordersCount: customer.ordersCount,
      joinedAt: customer.joinedAt,
      recentHistory: recentHistory.map((h: any) => ({
        action: h.action,
        points: h.points,
        description: h.description,
        timestamp: h.timestamp
      })),
      referralsCount
    };

    return json(customerData, {
      headers: {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "GET",
        "Access-Control-Allow-Headers": "Content-Type",
      },
    });
  } catch (error) {
    console.error("Error fetching customer data:", error);
    return json({ error: "Internal server error" }, { status: 500 });
  }
};

// Gérer les requêtes OPTIONS pour CORS
export const options = () => {
  return new Response(null, {
    status: 200,
    headers: {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "GET, OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type",
    },
  });
};
