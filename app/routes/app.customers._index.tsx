import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData, Link } from "@remix-run/react";
import {
  Page,
  Layout,
  Card,
  DataTable,
  Badge,
  Text,
  BlockStack,
  Button,
  Pagination,
  TextField,
  Select,
  Filters,
  ButtonGroup
} from "@shopify/polaris";
import { useState, useCallback, useEffect } from "react";
import { AdminLayout } from "app/components/Layout/AdminLayout";
import { authenticate } from "app/shopify.server";
import { getAllCustomers } from "app/models/Customer.server";
import { useTranslation } from "app/hooks/useTranslation";
import { useNavigate, useSearchParams, useNavigation } from "@remix-run/react";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const { session } = await authenticate.admin(request);
  const url = new URL(request.url);

  const page = parseInt(url.searchParams.get("page") || "1");
  const type = url.searchParams.get("type") || "";
  const search = url.searchParams.get("search") || "";

  const filters = { type, search };
  const result = await getAllCustomers(session.shop, page, 20, filters);

  return json({
    ...result,
    filters
  });
};

export default function Customers() {
  const { customers, total, pages, currentPage, filters } = useLoaderData<typeof loader>();
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const navigation = useNavigation();

  const isLoading = navigation.state === "loading";

  const [searchValue, setSearchValue] = useState(filters.search);
  const [typeFilter, setTypeFilter] = useState(filters.type);

  // Fonction pour construire l'URL avec les nouveaux paramètres
  const buildUrl = useCallback((newSearch?: string, newType?: string, newPage?: number) => {
    const params = new URLSearchParams();

    const search = newSearch !== undefined ? newSearch : searchValue;
    const type = newType !== undefined ? newType : typeFilter;
    const page = newPage !== undefined ? newPage : currentPage;

    if (search) params.set('search', search);
    if (type) params.set('type', type);
    if (page > 1) params.set('page', page.toString());

    return `/app/customers?${params.toString()}`;
  }, [searchValue, typeFilter, currentPage]);

  // Debounce pour la recherche
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (searchValue !== filters.search) {
        navigate(buildUrl(searchValue, typeFilter, 1));
      }
    }, 500);

    return () => clearTimeout(timeoutId);
  }, [searchValue, filters.search, navigate, buildUrl, typeFilter]);

  const handleSearchChange = useCallback((value: string) => {
    setSearchValue(value);
  }, []);

  const handleTypeFilterChange = useCallback((value: string) => {
    setTypeFilter(value);
    // Navigation immédiate pour les filtres
    navigate(buildUrl(searchValue, value, 1));
  }, [navigate, buildUrl, searchValue]);

  const clearFilters = useCallback(() => {
    setSearchValue("");
    setTypeFilter("");
    navigate("/app/customers");
  }, [navigate]);

  const formatCustomerName = (customer: any) => {
    const firstName = customer.firstName || "";
    const lastName = customer.lastName || "";
    if (firstName || lastName) {
      return `${firstName} ${lastName}`.trim();
    }
    return customer.email || `${t("admin.customers.member")} ${customer.customerId}`;
  };

  const rows = customers.map((customer: any) => [
    <Link
      key={customer.id}
      to={`/app/customers/${customer.id}`}
      style={{ textDecoration: "none", color: "inherit" }}
    >
      <Text as="span" fontWeight="medium">
        {formatCustomerName(customer)}
      </Text>
    </Link>,
    <Badge tone={customer.type === "member" ? "success" : "info"}>
      {customer.type === "member" ? t("admin.customers.member") : t("admin.customers.guest")}
    </Badge>,
    `${customer.points} ${t("admin.customers.points")}`,
    `${customer._count?.referrals || 0} ${t("admin.customers.referrals")}`,
    customer.email || "—",
    new Date(customer.joinedAt).toLocaleDateString("fr-FR")
  ]);

  const appliedFilters = [];
  if (typeFilter) {
    appliedFilters.push({
      key: "type",
      label: `${t("admin.customers.filters.type")}: ${typeFilter === "member" ? t("admin.customers.member") : t("admin.customers.guest")}`,
      onRemove: () => setTypeFilter("")
    });
  }
  if (searchValue) {
    appliedFilters.push({
      key: "search",
      label: `${t("common.search")}: ${searchValue}`,
      onRemove: () => setSearchValue("")
    });
  }

  const filters_components = [
    {
      key: "type",
      label: t("admin.customers.filters.type"),
      filter: (
        <Select
          label={t("admin.customers.type")}
          options={[
            { label: t("common.all"), value: "" },
            { label: t("admin.customers.member"), value: "member" },
            { label: t("admin.customers.guest"), value: "guest" }
          ]}
          value={typeFilter}
          onChange={handleTypeFilterChange}
        />
      )
    }
  ];

  return (
    <AdminLayout title={t("admin.customers.title")}>
      <Layout>
        <Layout.Section>
          <Card>
            <BlockStack gap="400">
              <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}>
                <Text as="h2" variant="headingMd">
                  {t("admin.customers.title")}
                </Text>
                <Text as="p" variant="bodyMd" tone="subdued">
                  {t("admin.customers.totalCustomers", { total })}
                </Text>
              </div>

              <Filters
                queryValue={searchValue}
                queryPlaceholder={t("admin.customers.filters.search")}
                filters={filters_components}
                appliedFilters={appliedFilters}
                onQueryChange={handleSearchChange}
                onQueryClear={() => setSearchValue("")}
                onClearAll={clearFilters}
              />

              {isLoading ? (
                <div style={{
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                  padding: "40px",
                  opacity: 0.6
                }}>
                  <Text as="p" variant="bodyMd">
                    {t("common.loading")}
                  </Text>
                </div>
              ) : (
                <DataTable
                  columnContentTypes={["text", "text", "text", "text", "text", "text"]}
                  headings={[
                    t("admin.customers.member"),
                    t("admin.customers.type"),
                    "Points",
                    "Parrainages",
                    t("admin.customers.email"),
                    t("admin.customers.joinedOn")
                  ]}
                  rows={rows}
                  footerContent={
                    pages > 1 ? (
                      <div style={{ display: "flex", justifyContent: "center", padding: "16px" }}>
                        <Pagination
                          hasPrevious={currentPage > 1}
                          onPrevious={() => {
                            navigate(buildUrl(searchValue, typeFilter, currentPage - 1));
                          }}
                          hasNext={currentPage < pages}
                          onNext={() => {
                            navigate(buildUrl(searchValue, typeFilter, currentPage + 1));
                          }}
                          label={t("admin.customers.pagination", { current: currentPage, total: pages })}
                        />
                      </div>
                    ) : undefined
                  }
                />
              )}
            </BlockStack>
          </Card>
        </Layout.Section>
      </Layout>
    </AdminLayout>
  );
}
