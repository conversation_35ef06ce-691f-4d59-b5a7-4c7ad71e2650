{"loyalty": {"points": {"balance": "Available Points", "earn": "Earn {{points}} points", "spend": "Spend your points"}, "referral": {"invite": "Invite a friend", "reward": "Earn {{points}} points for each referral"}, "vip": {"status": "VIP Status", "progress": "Progress to VIP status"}}, "common": {"save": "Save", "cancel": "Cancel", "loading": "Loading...", "error": "An error occurred", "search": "Search...", "logout": "Logout", "edit": "Edit", "delete": "Delete", "add": "Add", "create": "Create", "update": "Update", "view": "View", "back": "Back", "next": "Next", "previous": "Previous", "close": "Close", "confirm": "Confirm", "yes": "Yes", "no": "No", "active": "Active", "inactive": "Inactive", "enabled": "Enabled", "disabled": "Disabled", "success": "Success", "warning": "Warning", "info": "Information", "name": "Name", "description": "Description", "status": "Status", "actions": "Actions", "settings": "Settings", "configuration": "Configuration", "all": "All"}, "admin": {"navigation": {"dashboard": "Dashboard", "program": "Program", "customers": "Customers", "analytics": "Analytics", "settings": "Settings", "promotions": "Promotions", "history": "History", "pointsShop": "Points Shop", "overview": "Overview", "pointsConfig": "Points Configuration", "referrals": "Referrals", "vipProgram": "VIP Program", "bonusCampaigns": "Bonus Campaigns", "generalSettings": "General Settings", "customizeWidget": "Customize Widget", "exchangeableProducts": "Exchangeable Products"}, "dashboard": {"title": "Dashboard", "totalPoints": "Total Points", "activeMembers": "Active Members", "redemptionRate": "Redemption Rate", "pointsEvolution": "Points Evolution (30 days)", "pointsDistribution": "Points Distribution by Type", "recentActivity": "Recent Activity", "loadingChart": "Loading chart...", "tableHeaders": {"customer": "Customer", "action": "Action", "points": "Points", "date": "Date"}, "averagePointsPerCustomer": "Average points / customer", "dataAvailableSoon": "Data will be available soon...", "pointsInCirculation": "Points in circulation", "rewardsThisMonth": "Rewards this month"}, "program": {"title": "Loyalty Program", "overview": "Overview", "status": {"active": "Program Active", "inactive": "Program Inactive", "activate": "Activate", "deactivate": "Deactivate", "activeDescription": "Your loyalty program is currently active and your customers can earn points.", "inactiveDescription": "Your program is currently inactive. Activate it to allow your customers to earn points."}, "generalConfiguration": "General Configuration", "programName": "Program Name", "programDescription": "Program Description", "quickActions": "Quick Actions", "pointsConfiguration": "Points Configuration", "referralProgram": "Referral Program", "stats": {"title": "Statistics", "totalCustomers": "Total Customers", "activeCustomers": "Active Customers", "totalPointsEarned": "Total Points Earned", "totalPointsRedeemed": "Total Points Redeemed", "totalRewards": "Total Rewards", "pendingReferrals": "Pending Referrals", "completedReferrals": "Completed Referrals", "pointsDistributed": "Points Distributed"}, "paramSaveSuccess": "Parameters saved successfully", "paramSaveError": "Error saving parameters", "saveModifications": "Save Modifications", "saveDescription": "You have unsaved modifications"}, "customers": {"title": "Customers", "member": "Member", "guest": "Guest", "points": "points", "referrals": "person(s)", "email": "Email", "joinedOn": "Joined on", "type": "Type", "filters": {"type": "Type", "search": "Search by name or email"}, "pagination": "Page {{current}} of {{total}}", "totalCustomers": "{{total}} customer(s) total"}, "analytics": {"title": "Analytics", "subtitle": "Loyalty Program Analytics", "memberStats": "Member Statistics", "totalMembers": "Total Members", "newMembersLast30Days": "New Members (30 days)", "pointsTransactions": "Points Transactions", "totalTransactions": "Total Transactions", "pointsDistributed": "Points Distributed", "referralPurchases": "Referral Purchases", "referralRevenue": "Referral Revenue", "trends": "Trends", "membersGrowth": "Members Growth", "pointsGrowth": "Points Growth", "revenueGrowth": "Revenue Growth"}, "settings": {"title": "Settings", "quickNavigation": "Quick Navigation", "customizeWidget": "Customize Widget", "exchangeableProducts": "Exchangeable Products", "generalSettings": "General Settings", "shopName": "Shop Name", "currency": "<PERSON><PERSON><PERSON><PERSON>", "language": "Language", "emailNotifications": "Email Notifications", "pointsName": "Points Name", "welcomeMessage": "Welcome Message", "saveSuccess": "Setting<PERSON> saved successfully", "saveError": "Error saving settings"}, "points": {"title": "Points Configuration", "waysToEarn": "Ways to Earn Points", "waysToRedeem": "Ways to Redeem Points", "addWayToEarn": "Add Way to Earn", "addWayToRedeem": "Add Way to Redeem", "earnDescription": "Configure the different ways your customers can earn points. You can create actions with points per euro spent (e.g., 5 points/€1) or fixed points for specific actions (e.g., 100 points for registration).", "redeemDescription": "Configure the rewards your customers can get in exchange for their points.", "pointsPerEuro": "points/€1", "fixedPoints": "fixed points", "minPoints": "From {{points}} points", "exactPoints": "{{points}} points", "configurable": "Configurable", "fixed": "Fixed"}, "widget": {"title": "Widget Customization", "appearance": "Appearance", "colors": "Colors", "position": "Widget Position", "size": "Widget Size", "borders": "Borders", "shadow": "Drop Shadow", "animation": "Animation", "showPointsOnButton": "Show Points on Button", "primaryColor": "Primary Color", "secondaryColor": "Secondary Color", "textColor": "Text Color", "preview": "Preview", "previewDescription": "Real-time preview of your widget", "loyaltyProgram": "Loyalty Program", "welcomeMessage": "Welcome to our loyalty program!"}, "notifications": {"languageChanged": "Language changed to {{language}}"}}}