{"loyalty": {"points": {"balance": "Pontos disponíveis", "earn": "Ganhe {{points}} pontos", "spend": "Use seus pontos"}, "referral": {"invite": "Convide um amigo", "reward": "Ganhe {{points}} pontos por cada indicação"}, "vip": {"status": "Status VIP", "progress": "Progresso para status VIP"}}, "common": {"save": "<PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "loading": "Carregando...", "error": "Ocorreu um erro", "search": "Pesquisar...", "logout": "<PERSON><PERSON>", "edit": "<PERSON><PERSON>", "delete": "Excluir", "add": "<PERSON><PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON><PERSON>", "view": "Visualizar", "back": "Voltar", "next": "Próximo", "previous": "Anterior", "close": "<PERSON><PERSON><PERSON>", "confirm": "Confirmar", "yes": "<PERSON>m", "no": "Não", "active": "Ativo", "inactive": "Inativo", "enabled": "Habilitado", "disabled": "Desabilitado", "success": "Sucesso", "warning": "Aviso", "info": "Informação", "name": "Nome", "description": "Descrição", "status": "Status", "actions": "Ações", "settings": "Configurações", "configuration": "Configuração"}, "admin": {"navigation": {"dashboard": "<PERSON><PERSON>", "program": "Programa", "customers": "Clientes", "analytics": "<PERSON><PERSON><PERSON><PERSON>", "settings": "Configurações", "promotions": "Promoções", "history": "Hist<PERSON><PERSON><PERSON>", "pointsShop": "Loja de pontos", "overview": "Visão geral", "pointsConfig": "Configuração de pontos", "referrals": "Indicações", "vipProgram": "Programa VIP", "bonusCampaigns": "<PERSON><PERSON><PERSON>", "generalSettings": "Configurações gerais", "customizeWidget": "<PERSON><PERSON>r widget", "exchangeableProducts": "<PERSON><PERSON><PERSON> re<PERSON>"}, "dashboard": {"title": "<PERSON><PERSON>", "totalPoints": "Pontos totais", "activeMembers": "Membros ativos", "redemptionRate": "Taxa de resgate", "pointsEvolution": "Evolução dos pontos (30 dias)", "pointsDistribution": "Distribuição de pontos por tipo", "recentActivity": "Atividade recente", "loadingChart": "Carregando gráfico...", "tableHeaders": {"customer": "Cliente", "action": "Ação", "points": "Pontos", "date": "Data"}, "averagePointsPerCustomer": "Pontos médios / cliente", "dataAvailableSoon": "Os dados estarão disponíveis em breve...", "pointsInCirculation": "Pontos em circulação", "rewardsThisMonth": "Recompensas este mês"}, "program": {"title": "Programa de fidelidade", "overview": "Visão geral", "status": {"active": "Programa ativo", "inactive": "Programa inativo", "activate": "Ativar", "deactivate": "Desativar", "activeDescription": "Seu programa de fidelidade está atualmente ativo e seus clientes podem ganhar pontos.", "inactiveDescription": "Seu programa está atualmente inativo. Ative-o para permitir que seus clientes ganhem pontos."}, "generalConfiguration": "Configuração geral", "programName": "Nome do programa", "programDescription": "Descrição do programa", "quickActions": "Ações <PERSON>", "pointsConfiguration": "Configuração de pontos", "referralProgram": "Programa de indicações", "stats": {"title": "Estatísticas do programa", "totalCustomers": "Clientes totais", "activeCustomers": "Clientes ativos", "totalPointsEarned": "Pontos ganhos totais", "totalPointsRedeemed": "Pontos resgatados totais", "totalRewards": "Recompensas totais", "pendingReferrals": "Indicações pendentes", "completedReferrals": "Indicações concluídas", "pointsDistributed": "Pontos distribuídos"}, "paramSaveSuccess": "Parâmetros salvos com sucesso", "paramSaveError": "Erro ao salvar parâmet<PERSON>", "saveModifications": "Salvar modificaçõ<PERSON>", "saveDescription": "Você tem modificações não salvas"}, "customers": {"title": "Clientes", "member": "Membro", "guest": "Convidado", "points": "pontos", "referrals": "pessoa(s)", "email": "Email", "joinedOn": "<PERSON><PERSON><PERSON><PERSON> em", "type": "Tipo", "filters": {"type": "Tipo", "search": "Pesquisar por nome ou email"}, "pagination": "Página {{current}} de {{total}}"}, "analytics": {"title": "<PERSON><PERSON><PERSON><PERSON>", "subtitle": "Análises do programa de fidelidade", "memberStats": "Estatísticas de membros", "totalMembers": "<PERSON><PERSON><PERSON>", "newMembersLast30Days": "Novos membros (30 dias)", "pointsTransactions": "Transações de pontos", "totalTransactions": "Transações totais", "pointsDistributed": "Pontos distribuídos", "referralPurchases": "Compras por indicação", "referralRevenue": "Receita por indicação", "trends": "Tendências", "membersGrowth": "Crescimento de membros", "pointsGrowth": "Crescimento de pontos", "revenueGrowth": "Crescimento de receita"}, "settings": {"title": "Configurações", "quickNavigation": "Navegação rápida", "customizeWidget": "<PERSON><PERSON>r widget", "exchangeableProducts": "<PERSON><PERSON><PERSON> re<PERSON>", "generalSettings": "Configurações gerais", "shopName": "Nome da loja", "currency": "<PERSON><PERSON>", "language": "Idioma", "emailNotifications": "Notificações por email", "pointsName": "Nome dos pontos", "welcomeMessage": "Mensagem de boas-vindas", "saveSuccess": "Configurações salvas com sucesso", "saveError": "Erro ao salvar configurações"}, "points": {"title": "Configuração de pontos", "waysToEarn": "Formas de ganhar pontos", "waysToRedeem": "Formas de resgatar pontos", "addWayToEarn": "Adicionar forma de ganhar", "addWayToRedeem": "Adicionar forma de resgatar", "earnDescription": "Configure as diferentes formas que seus clientes podem ganhar pontos. Você pode criar ações com pontos por euro gasto (ex: 5 pontos/€1) ou pontos fixos para ações específicas (ex: 100 pontos para registro).", "redeemDescription": "Configure as recompensas que seus clientes podem obter em troca de seus pontos.", "pointsPerEuro": "pontos/€1", "fixedPoints": "pontos fixos", "minPoints": "A partir de {{points}} pontos", "exactPoints": "{{points}} pontos", "configurable": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fixed": "Fixo"}, "widget": {"title": "Personalização do widget", "appearance": "Aparência", "colors": "Cores", "position": "Posição do widget", "size": "Tamanho do widget", "borders": "<PERSON><PERSON><PERSON>", "shadow": "Sombra", "animation": "Animação", "showPointsOnButton": "Mostrar pontos no botão", "primaryColor": "<PERSON><PERSON>", "secondaryColor": "<PERSON><PERSON>", "textColor": "Cor do texto", "preview": "Visualização", "previewDescription": "Visualização em tempo real do seu widget", "loyaltyProgram": "Programa de Fidelidade", "welcomeMessage": "Bem-vindo ao nosso programa de fidelidade!"}, "notifications": {"languageChanged": "Idioma alterado para {{language}}"}}}