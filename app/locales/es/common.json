{"loyalty": {"points": {"balance": "Puntos disponibles", "earn": "<PERSON><PERSON> {{points}} puntos", "spend": "<PERSON><PERSON><PERSON>"}, "referral": {"invite": "Invitar a un amigo", "reward": "<PERSON>ane {{points}} puntos por cada referido"}, "vip": {"status": "Estado VIP", "progress": "Progreso hacia estado VIP"}}, "common": {"save": "Guardar", "cancel": "<PERSON><PERSON><PERSON>", "loading": "Cargando...", "error": "Se ha producido un error", "search": "Buscar...", "logout": "<PERSON><PERSON><PERSON>", "edit": "<PERSON><PERSON>", "delete": "Eliminar", "add": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "Actualizar", "view": "<PERSON>er", "back": "Atrás", "next": "Siguient<PERSON>", "previous": "Anterior", "close": "<PERSON><PERSON><PERSON>", "confirm": "Confirmar", "yes": "Sí", "no": "No", "active": "Activo", "inactive": "Inactivo", "enabled": "Habilitado", "disabled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "success": "Éxito", "warning": "Advertencia", "info": "Información", "name": "Nombre", "description": "Descripción", "status": "Estado", "actions": "Acciones", "settings": "Configuración", "configuration": "Configuración"}, "admin": {"navigation": {"dashboard": "Panel de control", "program": "Programa", "customers": "Clientes", "analytics": "<PERSON><PERSON><PERSON><PERSON>", "settings": "Configuración", "promotions": "Promociones", "history": "Historial", "pointsShop": "Tienda de puntos", "overview": "Resumen", "pointsConfig": "Configuración de puntos", "referrals": "Referencias", "vipProgram": "Programa VIP", "bonusCampaigns": "Campañas de bonificación", "generalSettings": "Configuración general", "customizeWidget": "<PERSON><PERSON>r widget", "exchangeableProducts": "Productos canjeables"}, "dashboard": {"title": "Panel de control", "totalPoints": "Puntos totales", "activeMembers": "Miembros activos", "redemptionRate": "<PERSON><PERSON>", "pointsEvolution": "Evolución de puntos (30 días)", "pointsDistribution": "Distribución de puntos por tipo", "recentActivity": "Actividad reciente", "loadingChart": "Cargando gráfico...", "tableHeaders": {"customer": "Cliente", "action": "Acción", "points": "Punt<PERSON>", "date": "<PERSON><PERSON>"}, "averagePointsPerCustomer": "Puntos promedio / cliente", "dataAvailableSoon": "Los datos estarán disponibles pronto...", "pointsInCirculation": "Puntos en circulación", "rewardsThisMonth": "Recompensas este mes"}, "program": {"title": "Programa de fidelidad", "overview": "Resumen", "status": {"active": "Programa activo", "inactive": "Programa inactivo", "activate": "Activar", "deactivate": "Desactivar", "activeDescription": "Su programa de fidelidad está actualmente activo y sus clientes pueden ganar puntos.", "inactiveDescription": "Su programa está actualmente inactivo. Actívelo para permitir que sus clientes ganen puntos."}, "generalConfiguration": "Configuración general", "programName": "Nombre del programa", "programDescription": "Descripción del programa", "quickActions": "Acciones rápidas", "pointsConfiguration": "Configuración de puntos", "referralProgram": "Programa de referencias", "stats": {"totalCustomers": "Clientes totales", "activeCustomers": "Clientes activos", "totalPointsEarned": "Puntos ganados totales", "totalPointsRedeemed": "Puntos canjeados totales", "totalRewards": "Recompensas totales", "pendingReferrals": "Referencias pendientes", "completedReferrals": "Referencias completadas", "pointsDistributed": "Puntos distribuidos"}, "paramSaveSuccess": "Parámetros guardados exitosamente", "paramSaveError": "Error al guardar los parámetros", "saveModifications": "Guardar modificaciones", "saveDescription": "Tiene modificaciones sin guardar"}, "customers": {"title": "Clientes", "member": "Miembro", "guest": "<PERSON><PERSON><PERSON><PERSON>", "points": "puntos", "referrals": "persona(s)", "email": "Email", "joinedOn": "Se unió el", "type": "Tipo", "filters": {"type": "Tipo", "search": "Buscar por nombre o email"}, "pagination": "Página {{current}} de {{total}}"}, "analytics": {"title": "<PERSON><PERSON><PERSON><PERSON>", "subtitle": "Análisis del programa de fidelidad", "memberStats": "Estadísticas de miembros", "totalMembers": "Miembros totales", "newMembersLast30Days": "Nuevos miembros (30 días)", "pointsTransactions": "Transacciones de puntos", "totalTransactions": "Transacciones totales", "pointsDistributed": "Puntos distribuidos", "referralPurchases": "Compras por referencia", "referralRevenue": "Ingresos por referencia", "trends": "Tendencias", "membersGrowth": "Crecimiento de miembros", "pointsGrowth": "Crecimiento de puntos", "revenueGrowth": "Crecimiento de ingresos"}, "settings": {"title": "Configuración", "quickNavigation": "Navegación rápida", "customizeWidget": "<PERSON><PERSON>r widget", "exchangeableProducts": "Productos canjeables", "generalSettings": "Configuración general", "shopName": "Nombre de la tienda", "currency": "Moneda", "language": "Idioma", "emailNotifications": "Notificaciones por email", "pointsName": "Nombre de los puntos", "welcomeMessage": "Mensaje de bienvenida", "saveSuccess": "Configuración guardada exitosamente", "saveError": "Error al guardar la configuración"}, "points": {"title": "Configuración de puntos", "waysToEarn": "Formas de ganar puntos", "waysToRedeem": "Formas de canjear puntos", "addWayToEarn": "<PERSON><PERSON><PERSON> de gana<PERSON>", "addWayToRedeem": "<PERSON><PERSON><PERSON> de can<PERSON>", "earnDescription": "Configure las diferentes formas en que sus clientes pueden ganar puntos. Puede crear acciones con puntos por euro gastado (ej: 5 puntos/€1) o puntos fijos para acciones específicas (ej: 100 puntos por registro).", "redeemDescription": "Configure las recompensas que sus clientes pueden obtener a cambio de sus puntos.", "pointsPerEuro": "puntos/€1", "fixedPoints": "puntos fijos", "minPoints": "Desde {{points}} puntos", "exactPoints": "{{points}} puntos", "configurable": "Configurable", "fixed": "<PERSON><PERSON>"}, "widget": {"title": "Personalización del widget", "appearance": "Apariencia", "colors": "Colores", "position": "Posición del widget", "size": "<PERSON><PERSON><PERSON> del widget", "borders": "<PERSON><PERSON>", "shadow": "Sombra", "animation": "Animación", "showPointsOnButton": "Mostrar puntos en el botón", "primaryColor": "Color principal", "secondaryColor": "Color secundario", "textColor": "Color del texto", "preview": "Vista previa", "previewDescription": "Vista previa en tiempo real de su widget", "loyaltyProgram": "Programa de Fidelidad", "welcomeMessage": "¡Bienvenido a nuestro programa de fidelidad!"}, "notifications": {"languageChanged": "Idioma cambiado a {{language}}"}}}