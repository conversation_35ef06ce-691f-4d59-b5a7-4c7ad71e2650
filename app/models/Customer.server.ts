import prisma from "../db.server";
import { getDefaultSiteSettings } from "./SiteSettings.server";

// Fonction pour s'assurer que les Settings existent pour un shop
async function ensureSettingsExist(shop: string) {
  try {
    const existingSettings = await prisma.settings.findUnique({
      where: { shop }
    });

    if (!existingSettings) {
      // Récupérer les paramètres par défaut depuis SiteSettings
      const defaultSettings = getDefaultSiteSettings(shop);

      // Créer les Settings par défaut
      await prisma.settings.create({
        data: {
          shop,
          earningRate: 1.0,
          redemptionRate: 0.01,
          minimumPoints: 100,
          expirationDays: 365,
          referralPoints: 100,
          birthdayPoints: 250,
          widgetEnabled: true,
          primaryColor: defaultSettings.widgetColor,
          widgetSecondaryColor: defaultSettings.widgetSecondaryColor,
          widgetTextColor: defaultSettings.widgetTextColor,
          widgetPosition: defaultSettings.widgetPosition,
          widgetSize: defaultSettings.widgetSize,
          widgetBorderRadius: defaultSettings.widgetBorderRadius,
          widgetShadow: defaultSettings.widgetShadow,
          widgetAnimation: defaultSettings.widgetAnimation,
          showPointsOnButton: defaultSettings.showPointsOnButton,
          pointsName: defaultSettings.pointsName,
          welcomeMessage: defaultSettings.welcomeMessage,
          shopName: defaultSettings.shopName,
          currency: defaultSettings.currency,
          emailNotifications: defaultSettings.emailNotifications,
          language: defaultSettings.language,
          customCSS: defaultSettings.customCSS
        }
      });
      console.log(`Settings créées pour le shop: ${shop}`);

      // Créer aussi les "Ways to Earn" par défaut
      await createDefaultWaysToEarn(shop);
    }
  } catch (error) {
    console.error("Erreur lors de la création des Settings:", error);
  }
}

// Fonction pour créer les "Ways to Earn" par défaut
async function createDefaultWaysToEarn(shop: string) {
  try {
    const existingWays = await prisma.wayToEarn.count({
      where: { shop }
    });

    if (existingWays === 0) {
      // Créer les façons de gagner par défaut
      await prisma.wayToEarn.createMany({
        data: [
          {
            shop,
            name: "Passer une commande",
            description: "Gagnez des points à chaque achat",
            actionType: "order",
            earningType: "increments",
            earningValue: 1.0,
            icon: "order",
            isActive: true
          },
          {
            shop,
            name: "Rejoindre le programme",
            description: "Points de bienvenue",
            actionType: "signup",
            earningType: "fixed",
            earningValue: 100,
            icon: "signup",
            isActive: true
          }
        ]
      });
      console.log(`Ways to Earn par défaut créées pour le shop: ${shop}`);
    }
  } catch (error) {
    console.error("Erreur lors de la création des Ways to Earn:", error);
  }
}

export interface Customer {
  id: string;
  customerId: string; // ID Shopify
  shop: string;
  firstName?: string;
  lastName?: string;
  email?: string;
  type: "member" | "guest";
  points: number;
  vipLevel?: string;
  totalSpent: number;
  ordersCount: number;
  lastOrderAt?: Date;
  joinedAt: Date;
  lastUpdated: Date;
}

export interface CreateCustomerData {
  customerId: string;
  shop: string;
  firstName?: string;
  lastName?: string;
  email?: string;
  type?: "member" | "guest";
}

export interface UpdateCustomerData {
  firstName?: string;
  lastName?: string;
  email?: string;
  type?: "member" | "guest";
  totalSpent?: number;
  ordersCount?: number;
  lastOrderAt?: Date;
}

export async function getAllCustomers(
  shop: string,
  page = 1,
  limit = 20,
  filters: { search?: string; type?: string } = {}
) {
  try {
    const skip = (page - 1) * limit;

    // Construire les conditions de filtrage
    const whereConditions: any = { shop };

    // Filtre par type (member/guest)
    if (filters.type) {
      whereConditions.type = filters.type;
    }

    // Filtre de recherche (nom, prénom, email)
    if (filters.search && filters.search.trim()) {
      const searchTerm = filters.search.trim();
      whereConditions.OR = [
        {
          firstName: {
            contains: searchTerm,
            mode: 'insensitive'
          }
        },
        {
          lastName: {
            contains: searchTerm,
            mode: 'insensitive'
          }
        },
        {
          email: {
            contains: searchTerm,
            mode: 'insensitive'
          }
        }
      ];
    }

    const [customers, total] = await Promise.all([
      prisma.customer.findMany({
        where: whereConditions,
        include: {
          referrals: {
            where: { status: "completed" },
            select: { id: true }
          },
          _count: {
            select: {
              referrals: {
                where: { status: "completed" }
              }
            }
          }
        },
        orderBy: { lastUpdated: "desc" },
        skip,
        take: limit
      }),
      prisma.customer.count({ where: whereConditions })
    ]);

    return {
      customers,
      total,
      pages: Math.ceil(total / limit),
      currentPage: page
    };
  } catch (error) {
    console.error("Error fetching customers:", error);
    return {
      customers: [],
      total: 0,
      pages: 0,
      currentPage: 1
    };
  }
}

export async function getCustomerById(id: string, shop: string) {
  try {
    return await prisma.customer.findFirst({
      where: { id, shop },
      include: {
        history: {
          orderBy: { timestamp: "desc" },
          take: 50
        },
        referrals: {
          include: {
            referred: {
              select: {
                firstName: true,
                lastName: true,
                email: true
              }
            }
          }
        },
        referredBy: {
          include: {
            referrer: {
              select: {
                firstName: true,
                lastName: true,
                email: true
              }
            }
          }
        },
        orders: {
          orderBy: { createdAt: "desc" },
          take: 20
        }
      }
    });
  } catch (error) {
    console.error("Error fetching customer:", error);
    return null;
  }
}

export async function getCustomerByShopifyId(customerId: string, shop: string) {
  try {
    return await prisma.customer.findFirst({
      where: { customerId, shop }
    });
  } catch (error) {
    console.error("Error fetching customer by Shopify ID:", error);
    return null;
  }
}

export async function createCustomer(data: CreateCustomerData) {
  try {
    // S'assurer que les Settings existent pour ce shop
    await ensureSettingsExist(data.shop);

    return await prisma.customer.create({
      data: {
        customerId: data.customerId,
        shop: data.shop,
        firstName: data.firstName,
        lastName: data.lastName,
        email: data.email,
        type: data.type || "guest"
      }
    });
  } catch (error) {
    console.error("Error creating customer:", error);
    return null;
  }
}

export async function updateCustomer(id: string, _shop: string, data: UpdateCustomerData) {
  try {
    return await prisma.customer.update({
      where: { id },
      data: {
        ...data,
        lastUpdated: new Date()
      }
    });
  } catch (error) {
    console.error("Error updating customer:", error);
    return null;
  }
}

export async function upsertCustomerFromShopify(shopifyCustomer: any, shop: string) {
  try {
    // S'assurer que les Settings existent pour ce shop
    await ensureSettingsExist(shop);

    const existingCustomer = await getCustomerByShopifyId(shopifyCustomer.id.toString(), shop);

    const customerData = {
      firstName: shopifyCustomer.first_name,
      lastName: shopifyCustomer.last_name,
      email: shopifyCustomer.email,
      totalSpent: parseFloat(shopifyCustomer.total_spent || "0"),
      ordersCount: shopifyCustomer.orders_count || 0,
      lastOrderAt: shopifyCustomer.last_order_date ? new Date(shopifyCustomer.last_order_date) : undefined
    };

    if (existingCustomer) {
      return await updateCustomer(existingCustomer.id, shop, customerData);
    } else {
      return await createCustomer({
        customerId: shopifyCustomer.id.toString(),
        shop,
        ...customerData,
        type: "guest" // Par défaut guest, devient member quand il s'inscrit
      });
    }
  } catch (error) {
    console.error("Error upserting customer from Shopify:", error);
    return null;
  }
}

export async function promoteToMember(customerId: string, shop: string) {
  try {
    const customer = await getCustomerByShopifyId(customerId, shop);
    if (!customer) return null;

    return await updateCustomer(customer.id, shop, { type: "member" });
  } catch (error) {
    console.error("Error promoting customer to member:", error);
    return null;
  }
}

export async function getCustomerStats(shop: string) {
  try {
    const [totalCustomers, members, guests, customersWithPoints] = await Promise.all([
      prisma.customer.count({ where: { shop } }),
      prisma.customer.count({ where: { shop, type: "member" } }),
      prisma.customer.count({ where: { shop, type: "guest" } }),
      prisma.customer.count({ where: { shop, points: { gt: 0 } } })
    ]);

    return {
      totalCustomers,
      members,
      guests,
      customersWithPoints
    };
  } catch (error) {
    console.error("Error fetching customer stats:", error);
    return {
      totalCustomers: 0,
      members: 0,
      guests: 0,
      customersWithPoints: 0
    };
  }
}

export async function getCustomersWithPointsLast30Days(shop: string) {
  try {
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    return await prisma.customer.count({
      where: {
        shop,
        points: { gt: 0 },
        lastUpdated: { gte: thirtyDaysAgo }
      }
    });
  } catch (error) {
    console.error("Error fetching customers with points last 30 days:", error);
    return 0;
  }
}

/**
 * Mettre à jour les points d'un client
 */
export async function updateCustomerPoints(customerId: string, shop: string, newPoints: number) {
  try {
    const customer = await getCustomerByShopifyId(customerId, shop);
    if (!customer) {
      return null;
    }

    return await prisma.customer.update({
      where: { id: customer.id },
      data: {
        points: newPoints,
        lastUpdated: new Date()
      }
    });
  } catch (error) {
    console.error("Error updating customer points:", error);
    return null;
  }
}
