import prisma from "../db.server";
import { getReferralSettings } from "./ReferralSettings.server";

export interface ReferralData {
  id: string;
  code: string;
  referralUrl: string;
  status: "pending" | "completed" | "expired";
  createdAt: Date;
  completedAt?: Date;
  expiresAt: Date;
  referredEmail?: string;
  pointsEarned?: number;
}

/**
 * Générer un token de parrainage unique (base64)
 */
function generateReferralCode(): string {
  // Générer un token plus long et plus sécurisé
  const array = new Uint8Array(32);
  if (typeof crypto !== 'undefined' && crypto.getRandomValues) {
    crypto.getRandomValues(array);
  } else {
    // Fallback pour Node.js
    const crypto = require('crypto');
    const buffer = crypto.randomBytes(32);
    for (let i = 0; i < 32; i++) {
      array[i] = buffer[i];
    }
  }

  // Convertir en base64 et nettoyer pour l'URL
  return Buffer.from(array).toString('base64')
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=/g, '');
}

/**
 * Créer un lien de parrainage pour un client
 */
export async function createReferralLink(
  shop: string,
  customerId: string
): Promise<{ success: boolean; referralUrl?: string; code?: string; error?: string }> {
  try {
    // Vérifier que le programme de parrainage est actif
    const settings = await getReferralSettings(shop);
    console.log("settings : ",settings, "shop : ", shop)
    if (!settings || !settings.active) {
      return { success: false, error: "Le programme de parrainage n'est pas actif" };
    }

    // Vérifier si le client existe
    const customer = await prisma.customer.findFirst({
      where: { customerId, shop }
    });

    if (!customer) {
      return { success: false, error: "Client non trouvé" };
    }

    // Vérifier s'il existe déjà un lien actif pour ce client
    const existingReferral = await prisma.referral.findFirst({
      where: {
        referrerId: customer.id,
        status: "pending",
        expiresAt: { gt: new Date() }
      }
    });

    if (existingReferral) {
      // Retourner le lien existant
      const referralUrl = `https://${shop}?ref=${existingReferral.code}`;
      return {
        success: true,
        referralUrl,
        code: existingReferral.code
      };
    }

    // Générer un nouveau code unique
    let code = generateReferralCode();
    let attempts = 0;
    const maxAttempts = 10;

    while (attempts < maxAttempts) {
      const existingCode = await prisma.referral.findUnique({
        where: { code }
      });

      if (!existingCode) {
        break;
      }

      code = generateReferralCode();
      attempts++;
    }

    if (attempts >= maxAttempts) {
      return { success: false, error: "Impossible de générer un code unique" };
    }

    // Calculer la date d'expiration
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + settings.expiryDays);

    // Créer le parrainage
    const referral = await prisma.referral.create({
      data: {
        shop,
        referrerId: customer.id,
        code,
        status: "pending",
        expiresAt
      }
    });

    // Construire l'URL de parrainage (redirection vers l'accueil)
    const referralUrl = `https://${shop}?ref=${code}`;

    return {
      success: true,
      referralUrl,
      code
    };

  } catch (error) {
    console.error("Erreur lors de la création du lien de parrainage:", error);
    return { success: false, error: "Erreur interne du serveur" };
  }
}

/**
 * Récupérer les parrainages d'un client
 */
export async function getCustomerReferrals(
  shop: string,
  customerId: string
): Promise<ReferralData[]> {
  try {
    const customer = await prisma.customer.findFirst({
      where: { customerId, shop }
    });

    if (!customer) {
      return [];
    }

    // Récupérer les paramètres de parrainage pour connaître les récompenses
    const settings = await getReferralSettings(shop);
    const referrerReward = settings?.referrerReward?.amount || 0;

    const referrals = await prisma.referral.findMany({
      where: { referrerId: customer.id },
      include: {
        referred: {
          select: {
            email: true,
            firstName: true,
            lastName: true
          }
        }
      },
      orderBy: { createdAt: "desc" }
    });

    return referrals.map(referral => ({
      id: referral.id,
      code: referral.code,
      referralUrl: `https://${shop}?ref=${referral.code}`,
      status: referral.status as "pending" | "completed" | "expired",
      createdAt: referral.createdAt,
      completedAt: referral.completedAt || undefined,
      expiresAt: referral.expiresAt,
      referredEmail: referral.referred?.email || undefined,
      pointsEarned: referral.status === "completed" ? referrerReward : undefined
    }));

  } catch (error) {
    console.error("Erreur lors de la récupération des parrainages:", error);
    return [];
  }
}

/**
 * Traiter un parrainage lors de l'inscription (attribution immédiate au filleul)
 */
export async function processReferralSignup(
  shop: string,
  referralCode: string,
  referredCustomerId: string
): Promise<{ success: boolean; error?: string }> {
  try {
    // Récupérer les paramètres de parrainage
    const settings = await getReferralSettings(shop);
    if (!settings || !settings.active) {
      return { success: false, error: "Programme de parrainage inactif" };
    }

    // Trouver le parrainage
    const referral = await prisma.referral.findFirst({
      where: {
        code: referralCode,
        status: "pending",
        expiresAt: { gt: new Date() }
      },
      include: {
        referrer: true
      }
    });

    if (!referral) {
      return { success: false, error: "Code de parrainage invalide ou expiré" };
    }

    // Trouver le client référé
    const referredCustomer = await prisma.customer.findFirst({
      where: { customerId: referredCustomerId, shop }
    });

    if (!referredCustomer) {
      return { success: false, error: "Client référé non trouvé" };
    }

    // Vérifier que le client ne se parraine pas lui-même
    if (referral.referrerId === referredCustomer.id) {
      return { success: false, error: "Auto-parrainage non autorisé" };
    }

    // Mettre à jour le parrainage avec le filleul (mais garder le statut "pending")
    await prisma.referral.update({
      where: { id: referral.id },
      data: {
        referredId: referredCustomer.id
      }
    });

    // Attribuer les points au filleul IMMÉDIATEMENT (récompense d'inscription)
    if (settings.referredReward.type === "points") {
      await prisma.pointsHistory.create({
        data: {
          ledgerId: referredCustomer.id,
          points: settings.referredReward.amount,
          action: "referred_signup",
          description: `Bonus d'inscription via parrainage`,
          metadata: JSON.stringify({
            referralCode,
            referrerCustomerId: referral.referrer.customerId,
            isSignup: true
          })
        }
      });

      // Mettre à jour le solde du filleul
      await prisma.customer.update({
        where: { id: referredCustomer.id },
        data: {
          points: { increment: settings.referredReward.amount }
        }
      });
    }

    console.log(`Parrainage d'inscription traité: filleul ${referredCustomer.email} a reçu ${settings.referredReward.amount} points`);
    return { success: true };

  } catch (error) {
    console.error("Erreur lors du traitement du parrainage d'inscription:", error);
    return { success: false, error: "Erreur interne du serveur" };
  }
}

/**
 * Valider un parrainage lors d'un achat (attribution au parrain si montant minimum atteint)
 */
export async function validateReferralPurchase(
  shop: string,
  referredCustomerId: string,
  orderTotal: number
): Promise<{ success: boolean; error?: string }> {
  try {
    // Récupérer les paramètres de parrainage
    const settings = await getReferralSettings(shop);
    if (!settings || !settings.active) {
      return { success: false, error: "Programme de parrainage inactif" };
    }

    // Trouver le client référé
    const referredCustomer = await prisma.customer.findFirst({
      where: { customerId: referredCustomerId, shop }
    });

    if (!referredCustomer) {
      return { success: false, error: "Client référé non trouvé" };
    }

    // Trouver un parrainage en attente pour ce client
    const referral = await prisma.referral.findFirst({
      where: {
        referredId: referredCustomer.id,
        status: "pending",
        expiresAt: { gt: new Date() }
      },
      include: {
        referrer: true
      }
    });

    if (!referral) {
      return { success: false, error: "Aucun parrainage en attente trouvé" };
    }

    // Vérifier que le montant minimum est atteint
    if (orderTotal < settings.minimumPurchase) {
      console.log(`Montant insuffisant pour valider le parrainage: ${orderTotal} < ${settings.minimumPurchase}`);
      return { success: false, error: "Montant minimum non atteint" };
    }

    // Valider le parrainage (changer le statut à "completed")
    await prisma.referral.update({
      where: { id: referral.id },
      data: {
        status: "completed",
        completedAt: new Date()
      }
    });

    // Attribuer les points au parrain (récompense de validation)
    if (settings.referrerReward.type === "points") {
      await prisma.pointsHistory.create({
        data: {
          ledgerId: referral.referrer.id,
          points: settings.referrerReward.amount,
          action: "referral_validated",
          description: `Parrainage validé - ${referredCustomer.email || 'un ami'} a effectué un achat de ${orderTotal}€`,
          metadata: JSON.stringify({
            referralCode: referral.code,
            referredCustomerId: referredCustomer.customerId,
            orderTotal,
            minimumPurchase: settings.minimumPurchase
          })
        }
      });

      // Mettre à jour le solde du parrain
      await prisma.customer.update({
        where: { id: referral.referrerId },
        data: {
          points: { increment: settings.referrerReward.amount }
        }
      });
    }

    console.log(`Parrainage validé: parrain ${referral.referrer.email} a reçu ${settings.referrerReward.amount} points`);
    return { success: true };

  } catch (error) {
    console.error("Erreur lors de la validation du parrainage:", error);
    return { success: false, error: "Erreur interne du serveur" };
  }
}

/**
 * Vérifier si un client a déjà un lien de parrainage actif
 */
export async function hasActiveReferralLink(shop: string, customerId: string): Promise<boolean> {
  try {
    const customer = await prisma.customer.findFirst({
      where: { customerId, shop }
    });

    if (!customer) {
      return false;
    }

    const activeReferral = await prisma.referral.findFirst({
      where: {
        referrerId: customer.id,
        status: "pending",
        expiresAt: { gt: new Date() }
      }
    });

    return !!activeReferral;
  } catch (error) {
    console.error("Erreur lors de la vérification du lien de parrainage:", error);
    return false;
  }
}

/**
 * Obtenir les statistiques de parrainage pour un shop
 */
export async function getReferralStats(shop: string) {
  try {
    const [totalReferrals, completedReferrals, pendingReferrals] = await Promise.all([
      prisma.referral.count({ where: { shop } }),
      prisma.referral.count({ where: { shop, status: "completed" } }),
      prisma.referral.count({ where: { shop, status: "pending" } })
    ]);

    return {
      total: totalReferrals,
      completed: completedReferrals,
      pending: pendingReferrals,
      conversionRate: totalReferrals > 0 ? (completedReferrals / totalReferrals) * 100 : 0
    };
  } catch (error) {
    console.error("Erreur lors de la récupération des statistiques:", error);
    return {
      total: 0,
      completed: 0,
      pending: 0,
      conversionRate: 0
    };
  }
}
