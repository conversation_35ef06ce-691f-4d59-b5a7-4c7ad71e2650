import prisma from "../db.server";

export interface ProgramStats {
  totalCustomers: number;
  activeCustomers: number;
  totalPointsEarned: number;
  totalPointsRedeemed: number;
  totalRewards: number;
  pendingReferrals: number;
  completedReferrals: number;
  pointsDistributed: number;
}

/**
 * Récupérer les statistiques du programme de fidélité
 */
export async function getProgramStats(shop: string): Promise<ProgramStats> {
  try {
    // Utiliser la même logique que app.admin.tsx qui fonctionne
    const pointsStats = await prisma.customer.aggregate({
      where: {
        shop: shop,
      },
      _sum: {
        points: true,
      },
      _count: {
        customerId: true,
      },
    });

    // Compter les clients actifs (avec des points)
    const activeCustomers = await prisma.customer.count({
      where: {
        shop,
        points: {
          gt: 0
        }
      }
    });

    // Calculer les points totaux gagnés et utilisés depuis pointsHistory
    const pointsEarned = await prisma.pointsHistory.aggregate({
      where: {
        customer: {
          shop: shop
        },
        action: "earn"
      },
      _sum: {
        points: true
      }
    });

    const pointsRedeemed = await prisma.pointsHistory.aggregate({
      where: {
        customer: {
          shop: shop
        },
        action: "redeem"
      },
      _sum: {
        points: true
      }
    });

    // Compter les récompenses distribuées
    const totalRewards = await prisma.reward.count({
      where: {
        shop
      }
    });

    // Statistiques de parrainage
    const pendingReferrals = await prisma.referral.count({
      where: {
        shop,
        status: "pending"
      }
    });

    const completedReferrals = await prisma.referral.count({
      where: {
        shop,
        status: "completed"
      }
    });

    // Points distribués via parrainage
    const referralPoints = await prisma.pointsHistory.aggregate({
      where: {
        customer: {
          shop: shop
        },
        action: "referral"
      },
      _sum: {
        points: true
      }
    });

    return {
      totalCustomers: pointsStats._count.customerId || 0,
      activeCustomers,
      totalPointsEarned: pointsEarned._sum.points || 0,
      totalPointsRedeemed: Math.abs(pointsRedeemed._sum.points || 0),
      totalRewards,
      pendingReferrals,
      completedReferrals,
      pointsDistributed: referralPoints._sum.points || 0
    };
  } catch (error) {
    console.error("Error fetching program stats:", error);
    // Retourner des statistiques par défaut en cas d'erreur
    return {
      totalCustomers: 0,
      activeCustomers: 0,
      totalPointsEarned: 0,
      totalPointsRedeemed: 0,
      totalRewards: 0,
      pendingReferrals: 0,
      completedReferrals: 0,
      pointsDistributed: 0
    };
  }
}

/**
 * Obtenir un exemple de client pour le simulateur
 */
export async function getSampleCustomerData(shop: string) {
  try {
    // Récupérer un client avec des points pour l'exemple
    const sampleCustomer = await prisma.customer.findFirst({
      where: {
        shop,
        points: {
          gt: 0
        }
      },
      orderBy: {
        points: "desc"
      }
    });

    if (sampleCustomer) {
      return {
        name: `${sampleCustomer.firstName} ${sampleCustomer.lastName}`,
        email: sampleCustomer.email,
        points: sampleCustomer.points,
        orders: 5, // Simulé pour l'exemple
        initials: `${sampleCustomer.firstName?.charAt(0) || 'J'}${sampleCustomer.lastName?.charAt(0) || 'D'}`
      };
    }

    // Données par défaut si aucun client trouvé
    return {
      name: "John Doe",
      email: "<EMAIL>",
      points: 1234,
      orders: 5,
      initials: "JD"
    };
  } catch (error) {
    console.error("Error fetching sample customer:", error);
    return {
      name: "John Doe",
      email: "<EMAIL>",
      points: 1234,
      orders: 5,
      initials: "JD"
    };
  }
}
