# Fonctionnalités à implémenter

## Section Programme (/app/program)

### Vue d'ensemble (_index.tsx) - Priorité Haute ✅
- [x] C<PERSON>er le modèle de données ProgramSettings dans la base de données
  - status (boolean)
  - name (string)
  - description (string)
  - created_at (datetime)
  - updated_at (datetime)
- [x] Créer l'API /api/program/settings (GET/POST)
- [x] Implémenter l'interface utilisateur avec Polaris
  - Banner pour le statut actif/inactif
  - Card pour les statistiques globales
  - Card pour l'activité récente
- [x] Ajouter les graphiques avec @shopify/polaris-viz
  - Évolution des points sur 30 jours
  - Distribution des points par type d'action
- [x] Intégrer la pagination pour l'activité récente

### Configuration des points (points.tsx) - Priorité Haute ✅
- [x] Étendre le modèle Settings avec les champs nécessaires
  - earningRate (float)
  - redemptionRate (float)
  - minimumPoints (integer)
  - expirationDays (integer)
  - referralPoints (integer)
  - birthdayPoints (integer)
- [x] Créer l'API /api/program/points/settings (GET/POST)
- [x] Implémenter le formulaire de configuration
  - Validation des taux
  - Prévisualisation en temps réel
  - Historique des modifications
- [x] **Ways to Earn (façons de gagner des points)**
  - [x] Modèle WayToEarn avec actionType (order, signup)
  - [x] Modals pour ajouter/modifier des façons de gagner
  - [x] Types de gains : points par euro ou points fixes
  - [x] Affichage des récompenses dans les cartes
  - [x] Intégration avec les webhooks
- [x] **Ways to Redeem (façons d'échanger des points)**
  - [x] Modèle WayToRedeem avec types d'échange
  - [x] Modals pour ajouter/modifier des façons d'échanger
  - [x] Types : réduction, produit gratuit, livraison gratuite
  - [x] Affichage du coût en points
- [x] **Base de données améliorée**
  - [x] Renommage "pointsLedger" → "customerPoints" (plus explicite)
  - [x] Service pointsService.server.ts pour l'attribution des points
  - [x] Gestion des types d'actions (order, signup)
- [x] Ajouter les webhooks pour la synchronisation
  - [x] ORDERS_CREATE pour le calcul des points (utilise WayToEarn)
  - [x] ORDERS_CANCELLED pour l'annulation des points
  - [x] ORDERS_FULFILLED pour la validation des points
- [x] **Système de notifications corrigé**
  - [x] Toasts fonctionnels
  - [x] Validation des formulaires
  - [x] Messages d'erreur appropriés
- [ ] Ajouter les tests pour chaque webhook
  - [ ] Tests unitaires des handlers
  - [ ] Tests d'intégration avec Shopify
  - [ ] Tests de scénarios d'erreur
- [ ] Créer les modèles d'emails de notification
  - Confirmation de gain de points
  - Rappel de points sur le point d'expirer
  - Changement de niveau VIP

### ✅ Gestion des clients et analyses (TERMINÉ)
- [x] **Modèle Customer étendu**
  - [x] Informations complètes : firstName, lastName, email
  - [x] Types : "member" (badge vert) et "guest" (badge bleu)
  - [x] Statistiques : points, totalSpent, ordersCount, parrainages
  - [x] Promotion automatique guest → member lors de l'inscription
- [x] **Page Clients** (`/app/customers`)
  - [x] Liste de tous les clients avec filtres et recherche
  - [x] Affichage : nom, type, points, parrainages, email, date d'inscription
  - [x] Pagination et navigation vers les détails
- [x] **Page détail client** (`/app/customers/$id`)
  - [x] Bouton retour et "Voir dans Shopify" avec lien externe
  - [x] Section gauche : infos client, activité (onglets points/parrainages/récompenses), commandes
  - [x] Section droite : points, statistiques, section parrainage (à développer)
- [x] **Page Analytics** (`/app/analytics`)
  - [x] Membres du programme (30 jours) avec mini graphique de tendance
  - [x] Points de transaction (30 jours) avec tendance
  - [x] Achats de parrainage (30 jours) avec tendance
  - [x] Statistiques globales : total clients, membres, invités, clients avec points
- [x] **Navigation mise à jour**
  - [x] Liens "Clients" et "Analyses" ajoutés au menu principal
  - [x] Routes fonctionnelles et cohérentes

### ✅ Interface client (storefront) - Complètement fonctionnelle avec vraies données
- [x] **Shopify Theme Extension** (extensions/custom-loyalty-app-rewards/)
  - [x] Configuration avancée dans shopify.extension.toml
  - [x] Widget flottant avec design extraordinaire
  - [x] Animations fluides et transitions CSS modernes
  - [x] Système de couleurs personnalisables via l'éditeur
- [x] **Design et UX exceptionnels**
  - [x] Bouton flottant avec animation de pulsation
  - [x] Panel coulissant avec header dégradé
  - [x] États multiples : chargement, invité, membre, erreur
  - [x] Cartes statistiques avec hover effects
  - [x] Barre de progression animée vers la prochaine récompense
- [x] **Fonctionnalités avancées**
  - [x] Avatar client avec initiales automatiques
  - [x] Badges de statut (Membre/Invité) avec couleurs
  - [x] Affichage des façons de gagner des points
  - [x] Actions rapides (récompenses, historique)
  - [x] Overlay avec fermeture par Escape
- [x] **Configuration flexible**
  - [x] 4 positions : bas droite/gauche, haut droite/gauche
  - [x] Couleurs personnalisables (principale/secondaire)
  - [x] Affichage mobile configurable
  - [x] Ouverture automatique pour nouveaux visiteurs
- [x] **Assets optimisés**
  - [x] CSS avec variables CSS et responsive design
  - [x] JavaScript modulaire avec classe LoyaltyWidget
  - [x] Chargement asynchrone pour performance
  - [x] Support du thème sombre automatique
- [x] **APIs intégrées**
  - [x] Endpoint /api/customer/$customerId pour données client
  - [x] Endpoint /api/ways-to-earn pour façons de gagner
  - [x] CORS configuré pour sécurité
  - [x] Gestion d'erreurs robuste
- [x] **Documentation mise à jour** (INSTALLATION_EMBED.md)
- [x] **Intégration des vraies données**
  - [x] API /api/customer/$customerId avec données complètes
  - [x] API /api/ways-to-earn pour affichage dynamique
  - [x] API /api/ways-to-redeem pour calcul des seuils
  - [x] API /api/loyalty/signup pour inscription au programme
  - [x] Synchronisation automatique des clients Shopify
  - [x] Calcul dynamique de la prochaine récompense
  - [x] Affichage des vraies façons de gagner des points
  - [x] Gestion des clients guest vs member
  - [x] Notifications de succès/erreur intégrées
  - [x] Webhooks customers pour synchronisation
  - [x] Mise à jour automatique des données client
- [x] **Migration vers App Proxy Shopify**
  - [x] Centralisation de toutes les APIs dans /api/proxy
  - [x] Routeur intelligent avec endpoints multiples
  - [x] URL stable qui ne change jamais
  - [x] Authentification automatique des clients
  - [x] Suppression des problèmes de CORS
  - [x] Communication sécurisée via Shopify
  - [x] Performance optimisée sans URL externe
- [x] **Interface client avancée - Fonctionnalités complètes**
  - [x] Affichage du nom et description du programme configurés par l'admin
  - [x] Vues intégrées pour récompenses et historique avec navigation
  - [x] Bouton de retour vers la page principale dans chaque vue
  - [x] Formatage intelligent des points (1k, 1.2k, 1M, etc.)
  - [x] Masquage automatique si programme désactivé par l'admin
  - [x] Respect des paramètres widgetEnabled depuis l'admin
  - [x] Application automatique des couleurs personnalisées
  - [x] Vue récompenses avec statut disponible/indisponible
  - [x] Vue historique avec icônes et formatage des dates
  - [x] États vides avec messages informatifs
  - [x] Design responsive pour toutes les nouvelles vues

### ✅ Système d'internationalisation (i18n) - Décembre 2024 ✅ **TERMINÉ**
- [x] **Hook de traduction personnalisé** (`app/hooks/useTranslation.ts`)
  - [x] Support de 8 langues : français, anglais, allemand, espagnol, italien, néerlandais, portugais, polonais
  - [x] Système de fallback intelligent (langue actuelle → anglais → français)
  - [x] Gestion des paramètres dynamiques dans les traductions
  - [x] Stockage de la langue sélectionnée en localStorage
  - [x] Changement de langue instantané avec re-render forcé
- [x] **Composant sélecteur de langue** (`app/components/LanguageSelector/`)
  - [x] Dropdown élégant avec drapeaux et noms de langues
  - [x] Intégration dans le header de l'interface admin
  - [x] Notification toast lors du changement de langue
  - [x] Style adaptatif et visible (correction du problème de visibilité)
- [x] **Fichiers de traduction étendus** (`app/locales/*/common.json`)
  - [x] Traductions complètes pour l'interface admin
  - [x] Navigation, dashboard, notifications, messages communs
  - [x] Structure hiérarchique organisée (admin.navigation, admin.dashboard, etc.)
  - [x] Cohérence entre toutes les langues supportées
- [x] **Interface admin traduite**
  - [x] AdminLayout avec navigation multilingue
  - [x] Page dashboard entièrement traduite
  - [x] Système de notifications avec ToastProvider
  - [x] Changement de langue en temps réel sans rechargement
- [x] **Système de notifications amélioré**
  - [x] ToastProvider global pour gestion centralisée
  - [x] Notifications de succès lors du changement de langue
  - [x] Messages traduits selon la langue sélectionnée
  - [x] Auto-dismiss après 3 secondes

### ✅ Corrections et améliorations récentes (Décembre 2024)
- [x] **Interface Client - Navigation Ways to Redeem**
  - [x] Vue dédiée créée avec navigation cohérente comme "Ways to Earn"
  - [x] Carte cliquable dans la page principale
  - [x] Section complète avec header, liste et bouton de retour
  - [x] Styles CSS adaptés et cohérents
  - [x] Animations et transitions fluides
- [x] **Interface Client - Titre du Programme**
  - [x] Nom dynamique depuis la configuration admin
  - [x] Couleur adaptative selon les paramètres du widget
  - [x] Fallback robuste avec "Programme de fidélité" par défaut
  - [x] Mise à jour garantie dans tous les états (guest/member)
- [x] **Interface Admin - Disposition Paramètres**
  - [x] Layout corrigé - suppression du double layout
  - [x] Disposition pleine largeur sans décalage
  - [x] Cohérence avec le pattern des autres sections admin
  - [x] Responsive design avec Layout.Section appropriés
- [x] **Interface Client - Bug "undefined Points"**
  - [x] Correction de `way.points` vers `way.earningValue`
  - [x] Gestion des types d'earning (increments vs fixed)
  - [x] Descriptions correctes dans "Ways to Earn"
- [x] **Système d'échange de récompenses** ✅ **TERMINÉ**
  - [x] API complète pour échanger points contre récompenses
  - [x] Génération automatique de codes Shopify (réduction, livraison gratuite)
  - [x] Interface client avec modal de succès et copie de code
  - [x] Déduction automatique des points et historique complet
  - [x] Gestion d'erreurs robuste et validation des permissions
  - [x] Support de tous les types : discount, shipping, product, coupon
  - [x] **Coupon configurable avec slider** - Interface utilisateur avancée
    - [x] Slider pour sélectionner le montant de points à échanger
    - [x] Calcul en temps réel de la valeur en euros (100 points = 1€)
    - [x] Modal de confirmation avec récapitulatif
    - [x] Loader pendant la génération du code
    - [x] Interface de résultat avec code généré et options copie/application
  - [x] **Bug Fix - Système d'échange unifié** ✅ **CORRIGÉ**
    - [x] Suppression des méthodes obsolètes utilisant confirm() JavaScript
    - [x] Unification de tous les types d'échange avec modal élégante
    - [x] Ajout de la méthode redeemCoupon() manquante
    - [x] Expérience utilisateur cohérente pour tous les types de récompenses
  - [x] **Bug Fix - PanelBody Undefined** ✅ **CORRIGÉ**
    - [x] Initialisation correcte de this.panelBody dans createElements()
    - [x] Vérifications de sécurité dans showConfigurableCouponSection()
    - [x] Gestion robuste des sections dynamiques dans hideAllSections()
    - [x] Interface de coupon configurable maintenant fonctionnelle
  - [x] **Bug Fix - Taux de Conversion et Validation** ✅ **CORRIGÉ**
    - [x] Validation adaptée selon le type (configurable vs fixe)
    - [x] Récupération du redemptionRate depuis la base de données
    - [x] Interface utilisateur avec calculs dynamiques en temps réel
    - [x] API program-info enrichie avec les paramètres de conversion
    - [x] Cohérence complète entre frontend, backend et base de données
  - [x] **Bug Fix - Authentification Shopify et Erreurs Prisma** ✅ **CORRIGÉ**
    - [x] Service d'authentification alternatif avec token stocké en base
    - [x] Migration vers client.request() (API moderne Shopify)
    - [x] Correction des champs Prisma (ledgerId au lieu de customerId)
    - [x] Sérialisation JSON correcte des metadata
    - [x] Génération de codes Shopify fonctionnelle pour tous les types
  - [x] **Système de Récompenses Complet** ✅ **TERMINÉ**
    - [x] Modèle Reward avec tous les champs nécessaires (status, dates, orderId)
    - [x] Génération et stockage automatique des récompenses lors d'échange
    - [x] Restriction des codes Shopify au client propriétaire
    - [x] Webhook de détection d'utilisation des codes dans les commandes
    - [x] API customer-rewards pour récupérer récompenses actives et passées
    - [x] Interface "Your rewards" avec sections séparées (disponibles/utilisées)
    - [x] Page de détail de récompense avec code copiable et application
    - [x] Mise à jour automatique du statut active → used via webhook
  - [x] **Bug Fix - Duplication des Points** ✅ **CORRIGÉ**
    - [x] Vérification de duplication dans awardPointsForOrder avant attribution
    - [x] Correction du return prématuré dans handleOrderCreated
    - [x] Attribution de points normale même avec codes de fidélité utilisés
    - [x] Idempotence garantie des webhooks order-created
    - [x] Intégrité des données et historique cohérent
  - [x] **Bug Fix - Logique Codes de Réduction** ✅ **CORRIGÉ**
    - [x] Correction erreur Prisma orderId (Int → String)
    - [x] Logique métier : pas de points normaux si codes de réduction utilisés
    - [x] Traitement correct des codes de fidélité (status → 'used')
    - [x] Règle claire : codes de réduction = pas de points normaux
    - [x] Gestion robuste des types et erreurs

### ✅ Fonctionnalités développées
- [x] **Système de parrainage complet** ✅ **TERMINÉ** 🎉
  - [x] Génération de codes/liens de parrainage uniques
  - [x] Interface de partage (email, réseaux sociaux) ✅ **FONCTIONNELLE**
  - [x] Suivi des parrainages en temps réel
  - [x] Attribution automatique des récompenses
- [ ] **Modèle Reward et échanges**
  - [ ] Système d'échange de points contre récompenses
  - [ ] Génération de codes de réduction
  - [ ] Gestion des produits gratuits et livraison gratuite
  - [ ] Historique des récompenses échangées
- [ ] **Notifications et emails**
  - [ ] Templates d'emails pour gains de points
  - [ ] Notifications de changement de niveau VIP
  - [ ] Rappels de points sur le point d'expirer
  - [ ] Emails de parrainage

### 🔄 Prochaines étapes prioritaires

#### 🎯 Fonctionnalités critiques à implémenter
1. **Système d'échange de récompenses en temps réel**
   - Bouton "Échanger" fonctionnel dans la vue récompenses
   - API d'échange avec validation des points et génération de codes Shopify
   - Déduction automatique des points et notifications de succès

2. **Configuration admin → Interface client automatique**
   - Textes personnalisables (titre, description, messages)
   - Couleurs et thème depuis les paramètres admin
   - Activation/désactivation en temps réel du widget

3. **Système de parrainage intégré**
   - Génération de codes de parrainage uniques
   - Partage sur réseaux sociaux depuis le widget
   - Attribution automatique des points de parrainage

4. **Gamification avancée**
   - Système de niveaux VIP avec badges
   - Bonus d'anniversaire automatiques
   - Défis et missions temporaires

5. **Analytics et insights**
   - Dashboard de performance du programme
   - Statistiques d'engagement du widget
   - ROI et taux de conversion

### ✅ Parrainage (referrals.tsx) - Priorité Moyenne 🔄 **EN COURS**
- [x] Créer le modèle ReferralSettings ✅ **TERMINÉ**
  - [x] referrer_reward (JSON)
  - [x] referee_reward (JSON)
  - [x] minimum_purchase_amount (float)
  - [x] expiration_days (integer)
- [x] Créer les APIs ✅ **INTERFACE ADMIN TERMINÉE**
  - [x] /api/program/referrals/settings (GET/POST)
  - [ ] /api/program/referrals/list (GET) 🔄 **EN COURS**
  - [ ] /api/program/referrals/generate-code (POST) 🔄 **EN COURS**
- [x] Implémenter l'interface de configuration ✅ **TERMINÉ**
  - [x] Formulaire des récompenses
  - [x] Personnalisation des messages
  - [ ] Tableau des parrainages actifs 🔄 **EN COURS**

### ✅ **SYSTÈME DE PARRAINAGE - IMPLÉMENTATION TERMINÉE** 🎉
- [x] **Service de génération de liens** (`app/models/Referral.server.ts`) ✅ **TERMINÉ**
  - [x] Génération de tokens uniques sécurisés (base64, 32 bytes)
  - [x] Création d'URLs de parrainage vers l'accueil (`https://shop.com?ref=TOKEN`)
  - [x] Gestion de l'expiration des liens (configurable via settings)
  - [x] Vérification des liens existants pour éviter les doublons
- [x] **APIs côté client** (via `/api/proxy`) ✅ **TERMINÉ**
  - [x] Endpoint `generate-referral` pour générer un lien de parrainage
  - [x] Endpoint `customer-referrals` pour récupérer les parrainages d'un client
  - [x] Intégration avec le widget client existant
- [x] **Traitement des parrainages** ✅ **TERMINÉ**
  - [x] Webhook pour détecter les commandes avec codes de parrainage
  - [x] Attribution automatique des récompenses (parrain + filleul)
  - [x] Mise à jour du statut des parrainages (pending → completed)
  - [x] Vérification du montant minimum d'achat
  - [x] Protection contre l'auto-parrainage
- [x] **Interface client** (widget existant connecté) ✅ **TERMINÉ**
  - [x] Récupération du vrai lien de parrainage depuis l'API
  - [x] Affichage du nombre de parrainages réels
  - [x] Historique des parrainages dans l'onglet dédié
  - [x] Capture automatique des codes de parrainage depuis l'URL
  - [x] Messages de partage personnalisés avec nom du programme
  - [x] Partage sur réseaux sociaux (Facebook, Twitter, Email)

### 🔧 **FONCTIONNALITÉS AVANCÉES DU PARRAINAGE**
- [x] **Capture de codes de parrainage** ✅ **TERMINÉ**
  - [x] Détection automatique du paramètre `?ref=CODE` dans l'URL
  - [x] Stockage en localStorage pour persistance
  - [x] Nettoyage de l'URL après capture
  - [x] Page de redirection `/pages/loyalty` pour les liens externes
- [x] **Traitement des commandes** ✅ **TERMINÉ**
  - [x] Extraction du code depuis les attributs de commande ou URL
  - [x] Validation du code et vérification de l'expiration
  - [x] Attribution des points selon les paramètres configurés
  - [x] Historique complet des transactions de parrainage
- [x] **Intégration checkout** ✅ **TERMINÉ**
  - [x] Ajout automatique du code aux attributs de panier
  - [x] Interception des boutons d'achat et checkout
  - [x] Persistance du code pendant toute la session
- [x] **Documentation et tests** ✅ **TERMINÉ**
  - [x] Documentation complète (REFERRAL_SYSTEM.md)
  - [x] Section de test intégrée dans la page parrainage admin
  - [x] Instructions de débogage et dépannage
- [x] **Améliorations finales** ✅ **TERMINÉ**
  - [x] Masquage automatique de la section parrainage si programme inactif
  - [x] Génération automatique de lien lors de l'inscription au programme
  - [x] Interface admin complète avec gestion des clients et génération de liens
  - [x] Format de liens optimisé avec tokens sécurisés
  - [x] Redirection directe vers l'accueil (suppression de /pages/loyalty)
- [x] **Corrections critiques** ✅ **TERMINÉ**
  - [x] Détection et traitement des codes de parrainage lors de l'inscription
  - [x] Correction de l'API signup pour créer les clients inexistants
  - [x] Attribution des récompenses de parrainage même sans commande
  - [x] Affichage du statut des liens de parrainage dans l'interface admin
  - [x] Nettoyage automatique du code de parrainage après utilisation
- [x] **Correction de la logique de parrainage** ✅ **TERMINÉ**
  - [x] Séparation inscription vs validation : filleul reçoit points à l'inscription
  - [x] Parrain reçoit points seulement quand filleul atteint montant minimum
  - [x] Deux fonctions distinctes : `processReferralSignup` et `validateReferralPurchase`
  - [x] Webhook corrigé pour valider les parrainages lors des achats
  - [x] Capture immédiate et persistance des codes de parrainage
  - [x] Script global de capture pour éviter la perte des paramètres URL

### Programme VIP (vip.tsx) - Priorité Moyenne
- [ ] Créer le modèle VIPLevel
  - name (string)
  - threshold (integer)
  - multiplier (float)
  - benefits (json)
- [ ] Créer les APIs
  - /api/program/vip/levels (GET/POST/PUT/DELETE)
  - /api/program/vip/customers (GET)
- [ ] Implémenter l'interface de gestion
  - CRUD des niveaux
  - Configuration des avantages
  - Liste des clients VIP
- [ ] Ajouter le calcul automatique des niveaux

### Campagnes bonus (campaigns.tsx) - Priorité Basse
- [ ] Créer le modèle Campaign
  - name (string)
  - type (enum)
  - multiplier (float)
  - start_date (datetime)
  - end_date (datetime)
  - conditions (json)
- [ ] Créer les APIs
  - /api/program/campaigns (GET/POST/PUT/DELETE)
  - /api/program/campaigns/active (GET)
- [ ] Implémenter l'interface de gestion
  - Création de campagnes
  - Sélection de produits/collections
  - Calendrier des campagnes

### Base de données - Priorité Haute ⏳
- [x] Créer les migrations pour les tables principales
- [x] Ajouter les relations entre les tables
- [ ] Créer les indexes pour les requêtes fréquentes
- [ ] Implémenter les triggers pour :
  - Mise à jour automatique des points
  - Calcul des niveaux VIP
  - Expiration des points

### Tests et Documentation - Priorité Haute 🔜
- [ ] Écrire les tests unitaires pour :
  - Calcul des points
  - Validation des données
  - Historisation des modifications
- [ ] Écrire les tests d'intégration pour :
  - Flux complet de commande
  - Synchronisation des points
  - Envoi des notifications
- [ ] Documenter :
  - Diagrammes de flux
  - Modèles de données
  - API endpoints
  - Guide d'administration
  - Guide d'utilisation client

### Interface client - Priorité Moyenne 🔜
- [ ] Créer le widget flottant
  - Affichage du solde de points
  - Historique des transactions
  - Options de rachat
- [ ] Implémenter la page de profil client
  - Vue d'ensemble des points
  - Historique détaillé
  - Statut VIP

### Optimisations - Priorité Basse 🔜
- [ ] Mise en cache des calculs fréquents
- [ ] Indexation des requêtes courantes
- [ ] Pagination des historiques
- [ ] Optimisation des requêtes N+1

Légende :
✅ Terminé
⏳ En cours
🔜 À venir
❌ Bloqué

## Base de données

### Nouvelles tables à créer
- [ ] VIPLevels : Gestion des niveaux VIP
- [ ] Campaigns : Campagnes de points bonus
- [ ] ReferralRules : Configuration du programme de parrainage
- [ ] SettingsHistory : Historique des modifications de configuration

### Modifications de tables existantes
- [ ] Ajouter champ vipLevel dans PointsLedger
- [ ] Ajouter champ campaignId dans PointsHistory
- [ ] Étendre Settings avec les configurations de parrainage

## Intégrations

### Shopify
- [ ] Synchroniser les commandes pour l'attribution des points
- [ ] Gérer les remboursements et annulations
- [ ] Intégrer les réductions basées sur les points
- [ ] Ajouter les métadonnées VIP aux clients

### Email
- [ ] Configurer les modèles d'emails pour :
  - Bienvenue dans le programme
  - Confirmation de gain de points
  - Changement de niveau VIP
  - Invitation de parrainage
  - Points sur le point d'expirer

## Optimisations futures
- [ ] Mise en cache des statistiques fréquemment consultées
- [ ] Système de file d'attente pour les calculs lourds
- [ ] Export des données en CSV/Excel
- [ ] API publique pour intégrations tierces
- [ ] Tableau de bord analytique avancé
