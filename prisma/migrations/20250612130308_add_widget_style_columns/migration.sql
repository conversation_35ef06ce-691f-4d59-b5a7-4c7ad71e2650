-- RedefineTables
PRAGMA defer_foreign_keys=ON;
PRAGMA foreign_keys=OFF;
CREATE TABLE "new_Settings" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "shop" TEXT NOT NULL,
    "earningRate" REAL NOT NULL DEFAULT 1.0,
    "redemptionRate" REAL NOT NULL DEFAULT 0.01,
    "minimumPoints" INTEGER NOT NULL DEFAULT 100,
    "expirationDays" INTEGER NOT NULL DEFAULT 365,
    "referralPoints" INTEGER NOT NULL DEFAULT 100,
    "birthdayPoints" INTEGER NOT NULL DEFAULT 250,
    "widgetEnabled" BOOLEAN NOT NULL DEFAULT true,
    "primaryColor" TEXT NOT NULL DEFAULT '#000000',
    "language" TEXT NOT NULL DEFAULT 'fr',
    "exchangeableProducts" TEXT,
    "widgetSecondaryColor" TEXT NOT NULL DEFAULT '#4CAF50',
    "widgetTextColor" TEXT NOT NULL DEFAULT '#FFFFFF',
    "widgetPosition" TEXT NOT NULL DEFAULT 'bottom-right',
    "widgetSize" TEXT NOT NULL DEFAULT 'medium',
    "widgetBorderRadius" TEXT NOT NULL DEFAULT 'rounded',
    "widgetShadow" BOOLEAN NOT NULL DEFAULT true,
    "widgetAnimation" BOOLEAN NOT NULL DEFAULT true,
    "showPointsOnButton" BOOLEAN NOT NULL DEFAULT true,
    "pointsName" TEXT NOT NULL DEFAULT 'Points',
    "welcomeMessage" TEXT NOT NULL DEFAULT 'Bienvenue dans notre programme de fidélité !',
    "shopName" TEXT,
    "currency" TEXT NOT NULL DEFAULT 'EUR',
    "customCSS" TEXT,
    "emailNotifications" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL
);
INSERT INTO "new_Settings" ("birthdayPoints", "createdAt", "earningRate", "exchangeableProducts", "expirationDays", "id", "language", "minimumPoints", "primaryColor", "redemptionRate", "referralPoints", "shop", "updatedAt", "widgetEnabled") SELECT "birthdayPoints", "createdAt", "earningRate", "exchangeableProducts", "expirationDays", "id", "language", "minimumPoints", "primaryColor", "redemptionRate", "referralPoints", "shop", "updatedAt", "widgetEnabled" FROM "Settings";
DROP TABLE "Settings";
ALTER TABLE "new_Settings" RENAME TO "Settings";
CREATE UNIQUE INDEX "Settings_shop_key" ON "Settings"("shop");
PRAGMA foreign_keys=ON;
PRAGMA defer_foreign_keys=OFF;
