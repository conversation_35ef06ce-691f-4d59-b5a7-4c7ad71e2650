/*
  Warnings:

  - You are about to drop the `PointsLedger` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropIndex
DROP INDEX "PointsLedger_customerId_shop_key";

-- DropIndex
DROP INDEX "PointsLedger_shop_idx";

-- DropTable
PRAGMA foreign_keys=off;
DROP TABLE "PointsLedger";
PRAGMA foreign_keys=on;

-- CreateTable
CREATE TABLE "customer_points" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "customerId" TEXT NOT NULL,
    "shop" TEXT NOT NULL,
    "points" INTEGER NOT NULL DEFAULT 0,
    "vipLevel" TEXT,
    "lastUpdated" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "customer_points_shop_fkey" FOREIGN KEY ("shop") REFERENCES "Settings" ("shop") ON DELETE RESTRICT ON UPDATE CASCADE
);

-- RedefineTables
PRAGMA defer_foreign_keys=ON;
PRAGMA foreign_keys=OFF;
CREATE TABLE "new_PointsHistory" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "ledgerId" TEXT NOT NULL,
    "action" TEXT NOT NULL,
    "points" INTEGER NOT NULL,
    "description" TEXT,
    "metadata" TEXT,
    "timestamp" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "expiresAt" DATETIME,
    CONSTRAINT "PointsHistory_ledgerId_fkey" FOREIGN KEY ("ledgerId") REFERENCES "customer_points" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
);
INSERT INTO "new_PointsHistory" ("action", "description", "expiresAt", "id", "ledgerId", "metadata", "points", "timestamp") SELECT "action", "description", "expiresAt", "id", "ledgerId", "metadata", "points", "timestamp" FROM "PointsHistory";
DROP TABLE "PointsHistory";
ALTER TABLE "new_PointsHistory" RENAME TO "PointsHistory";
CREATE INDEX "PointsHistory_ledgerId_idx" ON "PointsHistory"("ledgerId");
CREATE INDEX "PointsHistory_timestamp_idx" ON "PointsHistory"("timestamp");
CREATE TABLE "new_Referral" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "shop" TEXT NOT NULL,
    "referrerId" TEXT NOT NULL,
    "referredId" TEXT,
    "code" TEXT NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'pending',
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "completedAt" DATETIME,
    "expiresAt" DATETIME NOT NULL,
    CONSTRAINT "Referral_referrerId_fkey" FOREIGN KEY ("referrerId") REFERENCES "customer_points" ("id") ON DELETE RESTRICT ON UPDATE CASCADE,
    CONSTRAINT "Referral_referredId_fkey" FOREIGN KEY ("referredId") REFERENCES "customer_points" ("id") ON DELETE SET NULL ON UPDATE CASCADE
);
INSERT INTO "new_Referral" ("code", "completedAt", "createdAt", "expiresAt", "id", "referredId", "referrerId", "shop", "status") SELECT "code", "completedAt", "createdAt", "expiresAt", "id", "referredId", "referrerId", "shop", "status" FROM "Referral";
DROP TABLE "Referral";
ALTER TABLE "new_Referral" RENAME TO "Referral";
CREATE UNIQUE INDEX "Referral_code_key" ON "Referral"("code");
CREATE INDEX "Referral_shop_idx" ON "Referral"("shop");
CREATE INDEX "Referral_referrerId_idx" ON "Referral"("referrerId");
CREATE INDEX "Referral_code_idx" ON "Referral"("code");
CREATE TABLE "new_WayToEarn" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "shop" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "actionType" TEXT NOT NULL DEFAULT 'order',
    "earningType" TEXT NOT NULL DEFAULT 'increments',
    "earningValue" REAL NOT NULL,
    "icon" TEXT NOT NULL DEFAULT 'order',
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    CONSTRAINT "WayToEarn_shop_fkey" FOREIGN KEY ("shop") REFERENCES "Settings" ("shop") ON DELETE RESTRICT ON UPDATE CASCADE
);
INSERT INTO "new_WayToEarn" ("createdAt", "description", "earningType", "earningValue", "icon", "id", "isActive", "name", "shop", "updatedAt") SELECT "createdAt", "description", "earningType", "earningValue", "icon", "id", "isActive", "name", "shop", "updatedAt" FROM "WayToEarn";
DROP TABLE "WayToEarn";
ALTER TABLE "new_WayToEarn" RENAME TO "WayToEarn";
CREATE INDEX "WayToEarn_shop_idx" ON "WayToEarn"("shop");
CREATE INDEX "WayToEarn_isActive_idx" ON "WayToEarn"("isActive");
CREATE INDEX "WayToEarn_actionType_idx" ON "WayToEarn"("actionType");
PRAGMA foreign_keys=ON;
PRAGMA defer_foreign_keys=OFF;

-- CreateIndex
CREATE INDEX "customer_points_shop_idx" ON "customer_points"("shop");

-- CreateIndex
CREATE UNIQUE INDEX "customer_points_customerId_shop_key" ON "customer_points"("customerId", "shop");
