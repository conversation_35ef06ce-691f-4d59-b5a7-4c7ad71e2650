# Documentation Technique - Programme de Fidélité

## Structure du Projet

### Base de données (Prisma)
Les modèles suivants sont définis dans `prisma/schema.prisma` :

- **CustomerPoints** : Gestion des soldes de points des clients
  - `id`: Identifiant unique
  - `customerId`: ID du client Shopify
  - `shop`: Nom de la boutique
  - `points`: Solde actuel
  - `vipLevel`: Niveau VIP actuel
  - `lastUpdated`: <PERSON><PERSON><PERSON> mise à jour

- **PointsHistory** : Historique des transactions
  - `id`: Identifiant unique
  - `ledgerId`: Référence au CustomerPoints
  - `action`: Type d'action (earn, redeem, referral, signup, etc.)
  - `points`: Nombre de points
  - `description`: Description de la transaction
  - `metadata`: Données supplémentaires (JSON)

- **WayToEarn** : Façons de gagner des points
  - `id`: Identifiant unique
  - `shop`: Nom de la boutique
  - `name`: Nom de l'action
  - `description`: Description
  - `actionType`: Type d'action (order, signup, etc.)
  - `earningType`: Type de gain (increments, fixed)
  - `earningValue`: Valeur du gain
  - `icon`: Icône associée
  - `isActive`: Statut actif/inactif

- **WayToRedeem** : Façons d'échanger des points
  - `id`: Identifiant unique
  - `shop`: Nom de la boutique
  - `name`: Nom de l'échange
  - `description`: Description
  - `redeemType`: Type d'échange (discount, product, shipping)
  - `redeemValue`: Valeur de l'échange
  - `pointsCost`: Coût en points
  - `icon`: Icône associée
  - `isActive`: Statut actif/inactif

- **Settings** : Configuration par boutique
  - `id`: Identifiant unique
  - `shop`: Nom de la boutique
  - `earningRate`: Taux de gain
  - `redemptionRate`: Taux de conversion
  - `vipThreshold`: Seuil VIP
  - `referralPoints`: Points de parrainage
  - `birthdayPoints`: Points d'anniversaire

- **PointsProduct** : Produits de la boutique de points
  - `id`: Identifiant unique
  - `shop`: Nom de la boutique
  - `title`: Titre du produit
  - `pointsCost`: Coût en points
  - `type`: Type de récompense (coupon, shipping, product, percentage)
  - `image`: URL de l'image
  - `status`: Statut (active, draft)
  - `metadata`: Configuration spécifique au type (JSON)

### Interface Administrateur

#### Layout (`app/components/Layout/AdminLayout.tsx`)
Composant de mise en page principal avec :
- Navigation latérale
- Gestion des routes
- Intégration Polaris

#### Hooks personnalisés

- **useHydrated** (`app/hooks/useHydrated.ts`)
  - Gère l'hydratation des composants côté client
  - Utilisé pour les composants qui nécessitent window/document
  - Retourne un booléen indiquant si le composant est hydraté

#### Routes Admin

1. **Dashboard** (`app/routes/app.admin.tsx`)
   - Affichage des statistiques globales :
     - Points totaux en circulation
     - Nombre de membres actifs
     - Taux d'utilisation des points
   - Visualisations graphiques (chargement côté client) :
     - Graphique linéaire de l'évolution des points sur 30 jours
     - Graphique en barres de la distribution des points par type d'action
   - Liste des activités récentes avec :
     - Identifiant du client
     - Type d'action
     - Montant des points
     - Date de la transaction
   - Intégration avec la base de données :
     - Requêtes agrégées sur `PointsLedger` pour les totaux
     - Groupement des données de `PointsHistory` pour les graphiques
     - Pagination des activités récentes (10 dernières entrées)
   - Utilisation des composants :
     - Polaris Grid et Card pour la mise en page
     - @shopify/polaris-viz pour les graphiques (chargement dynamique)
     - DataTable pour l'affichage des activités
   - Optimisations :
     - Chargement paresseux des graphiques
     - Gestion de l'hydratation pour le SSR
     - États de chargement appropriés

2. **Règles** (`app/routes/app.admin.rules.tsx`)
   - Configuration des taux de points
   - Gestion des seuils VIP
   - Formulaire de configuration avec validation

3. **Promotions** (`app/routes/app.admin.promotions.tsx`)
   - Liste des promotions actives/programmées
   - Modal de création de promotion
   - Gestion des multiplicateurs de points

4. **Historique** (`app/routes/app.admin.history.tsx`)
   - Tableau des transactions de points
   - Filtres par période et recherche
   - Calcul des totaux

5. **Boutique de points** (`app/routes/app.admin.points-shop.tsx`)
   - Liste des récompenses disponibles
   - Gestion des types de récompenses :
     - Bons d'achat
     - Livraison gratuite
     - Produits physiques
     - Réductions pourcentage
   - Modal de création avec formulaire
   - Statuts actif/brouillon

6. **Configuration des points** (`app/routes/app.program.points.tsx`)
   - Interface de configuration des taux de points
     - Taux de gain (points/€)
     - Taux de conversion (€/point)
     - Points minimum pour échanger
     - Expiration des points (jours)
     - Points de parrainage
     - Points d'anniversaire
   - Prévisualisation en temps réel
     - Calcul des points gagnés
     - Calcul de la valeur en euros
   - Validation des données
     - Vérification des valeurs positives
     - Gestion des erreurs avec bannières
     - Messages d'erreur personnalisés
   - Notifications utilisateur
     - Toasts pour les actions réussies
     - Durée de 4 secondes
     - Messages contextuels
   - Optimisations
     - Mise à jour en temps réel
     - Gestion du state avec React hooks
     - Validation côté client et serveur
   - Historique des modifications
     - Stockage des anciennes et nouvelles valeurs
     - Traçabilité des changements
   - API dédiée
     - GET /api/program/points/settings
     - POST /api/program/points/settings
   - Composants utilisés
     - Polaris Form et TextField
     - Card pour la mise en page
     - Banner pour les erreurs
     - BlockStack pour l'espacement

### Internationalisation
- Fichiers de traduction dans `app/locales/`
- Support pour : fr, en, de, es, it, nl, pt, pl
- Structure commune pour tous les fichiers :
  ```json
  {
    "loyalty": {
      "points": {},
      "referral": {},
      "vip": {}
    },
    "common": {}
  }
  ```

## Guide d'Implémentation

### Ajout d'une Nouvelle Fonctionnalité
1. Créer/modifier le modèle Prisma si nécessaire
2. Ajouter les traductions dans tous les fichiers locales
3. Créer les composants nécessaires
4. Implémenter la route correspondante
5. Mettre à jour la documentation

### Bonnes Pratiques
- Utiliser les composants Polaris pour l'interface
- Documenter en français les modèles Prisma
- Suivre la structure de routing Remix
- Maintenir la cohérence des traductions

### Types de Récompenses
1. **Bons d'achat**
   - Génération automatique de codes de réduction
   - Montant fixe en devise

2. **Livraison gratuite**
   - Application sur la prochaine commande
   - Durée de validité configurable

3. **Produits physiques**
   - Lien avec les produits Shopify
   - Gestion des variantes

4. **Réductions pourcentage**
   - Pourcentage de réduction
   - Plafond optionnel

## Dépendances et Imports

### Dépendances principales
```json
{
  "@remix-run/node": "^2.16.7",
  "@remix-run/react": "^2.16.7",
  "@shopify/polaris": "^12.0.0",
  "@shopify/shopify-app-remix": "^3.7.0",
  "@shopify/polaris-viz": "^9.0.0",
  "prisma": "^5.0.0",
  "@prisma/client": "^5.0.0",
  "react": "^18.2.0",
  "react-dom": "^18.2.0"
}
```

### Imports par fonctionnalité

#### Interface Admin (`app/routes/app.admin.tsx`)
```typescript
// Remix et React
import { json, LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import { lazy, Suspense } from "react";

// Composants Polaris
import {
  Page,
  Layout,
  Card,
  Text,
  Grid,
  BlockStack,
  DataTable
} from "@shopify/polaris";

// Graphiques (chargement dynamique)
const LineChart = lazy(() => import("@shopify/polaris-viz").then(module => ({ default: module.LineChart })));
const BarChart = lazy(() => import("@shopify/polaris-viz").then(module => ({ default: module.BarChart })));

// Hooks personnalisés
import { useHydrated } from "../hooks/useHydrated";

// Services
import db from "../db.server";
import { authenticate } from "app/shopify.server";
```

#### Base de données (`prisma/schema.prisma`)
```prisma
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}
```

### Installation des dépendances
```bash
# Installation des dépendances de base
npm install

# Installation des dépendances de développement
npm install --save-dev @types/react @types/node

# Installation de Prisma
npm install prisma --save-dev
npm install @prisma/client

# Installation des composants graphiques
npm install @shopify/polaris-viz
```

### Variables d'environnement requises
```env
DATABASE_URL="postgresql://user:password@localhost:5432/dbname"
SHOPIFY_API_KEY="your_api_key"
SHOPIFY_API_SECRET="your_api_secret"
```

## Points et Récompenses

### Configuration des points (`app/routes/app.program.points.tsx`)
- Interface de configuration des taux de points
  - Taux de gain (points/€)
  - Taux de conversion (€/point)
  - Points minimum pour échanger
  - Expiration des points (jours)
  - Points de parrainage
  - Points d'anniversaire
- Prévisualisation en temps réel
  - Calcul des points gagnés
  - Calcul de la valeur en euros
- Validation des données
  - Vérification des valeurs positives
  - Gestion des erreurs avec bannières
  - Messages d'erreur personnalisés
- Notifications utilisateur
  - Toasts pour les actions réussies
  - Durée de 4 secondes
  - Messages contextuels
- Optimisations
  - Mise à jour en temps réel
  - Gestion du state avec React hooks
  - Validation côté client et serveur

### API Points (`app/routes/api.program.points.settings.ts`)
- Endpoints :
  - POST /api/program/points/settings
    - Action "update" : mise à jour d'un champ unique
    - Action "updateAll" : mise à jour de tous les champs
- Validation des données
  - Vérification des valeurs positives
  - Vérification des champs requis
- Historisation des modifications
  - Stockage des anciennes valeurs
  - Stockage des nouvelles valeurs
  - Horodatage des modifications

## Prochaines étapes à réaliser

### 1. Webhooks pour la synchronisation des points
- [ ] Webhooks à implémenter :
  ```typescript
  // Types de webhooks
  type WebhookType = 
    | 'ORDERS_CREATE'    // Calcul automatique des points
    | 'ORDERS_CANCELLED' // Annulation des points
    | 'ORDERS_FULFILLED' // Validation des points
  
  // Structure des handlers
  interface WebhookHandler {
    topic: WebhookType;
    path: string;
    handler: (topic: string, shop_domain: string, body: any) => Promise<void>;
  }
  ```
- [ ] Points à considérer :
  - Validation des commandes
  - Gestion des remboursements partiels
  - Historique des transactions
  - Notifications aux clients

### 2. Système de notification
- [ ] Créer les modèles d'emails :
  - Confirmation de gain de points
  - Rappel de points sur le point d'expirer
  - Changement de niveau VIP
- [ ] Implémenter le système d'envoi d'emails
- [ ] Configurer les déclencheurs automatiques

### 3. Interface client
- [ ] Créer le widget flottant
  - Affichage du solde de points
  - Historique des transactions
  - Options de rachat
- [ ] Implémenter la page de profil client
  - Vue d'ensemble des points
  - Historique détaillé
  - Statut VIP

### 4. Tests et optimisations
- [ ] Écrire les tests unitaires pour :
  - Calcul des points
  - Validation des données
  - Historisation des modifications
- [ ] Écrire les tests d'intégration pour :
  - Flux complet de commande
  - Synchronisation des points
  - Envoi des notifications
- [ ] Optimiser les performances :
  - Mise en cache des calculs fréquents
  - Indexation des requêtes courantes
  - Pagination des historiques

### 5. Documentation
- [ ] Compléter la documentation technique :
  - Diagrammes de flux
  - Modèles de données
  - API endpoints
- [ ] Créer la documentation utilisateur :
  - Guide d'administration
  - Guide d'utilisation client
  - FAQ
